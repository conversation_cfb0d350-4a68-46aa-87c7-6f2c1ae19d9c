# 钉钉通知集成使用指南

## 📋 概述

事件合约钉钉通知器为自动化交易系统提供完整的钉钉消息推送功能，支持交易信号、结算通知、风险提醒和每日总结。

## 🚀 快速开始

### 1. 基本配置

```python
from quant.strategies.event_contract_dingtalk_notifier import (
    EventContractDingtalkNotifier,
    NotificationConfig
)

# 创建通知配置
config = NotificationConfig(
    enable_signal_notification=True,      # 启用交易信号通知
    enable_settlement_notification=True,  # 启用结算通知
    enable_risk_notification=True,        # 启用风险提醒
    enable_daily_summary=True,            # 启用每日总结
    
    # 钉钉Webhook URL（从钉钉群组机器人获取）
    dingtalk_token="your_dingtalk_webhook_url",
    
    # 通知频率控制
    min_signal_interval=300,    # 最小信号通知间隔（秒）
    max_daily_notifications=50  # 每日最大通知数
)

# 创建通知器
notifier = EventContractDingtalkNotifier(config)
```

### 2. 集成到交易系统

```python
from quant.strategies.event_contract_decision_engine import EventContractDecisionEngine
from quant.strategies.event_contract_signal_generator_simple import SignalResult

# 在交易决策后发送通知
def execute_trade(signal_result, decision, market_data):
    # 发送交易信号通知
    if decision.should_trade:
        success, error = notifier.send_trading_signal(
            decision=decision,
            signal_result=signal_result,
            market_data=market_data
        )
        
        if success:
            print("✅ 交易信号通知发送成功")
        else:
            print(f"❌ 通知发送失败: {error}")
```

## 💬 消息类型

### 1. 交易信号通知

**触发条件**: 决策引擎确认执行交易时

**包含内容**:
- 🚀 交易方向（UP/DOWN）
- 💰 投注金额
- 📊 信心度和技术分析评分
- ⚠️ 风险等级和市场条件
- 💡 市场提醒信息

**关键词**: 必须包含"交易"和"小火箭"🚀

**示例消息**:
```
### 🎯 **交易**信号通知 🚀

> **交易方向:** UP 🚀
> **投注金额:** 25.00 USDT
> **信心度:** 88.0%
> **技术分析:** 82.0分
> **风险等级:** LOW
> **市场条件:** trending
> **当前价格:** 95000.0
> **决策原因:** 满足交易条件：信心度88.0%，风险low
> **市场提醒:** 强势上涨信号
> **信号时间:** 2025-07-12 09:30:05

🎉 祝**交易**顺利！🚀小火箭🚀起飞！
```

### 2. 结算通知

**触发条件**: 10分钟合约到期结算时

**包含内容**:
- 📊 订单编号和交易方向
- 💰 投注金额和盈亏结果
- 📈 收益率计算
- 🎉 鼓励或安慰信息

**示例消息**:
```
### 📊 **交易**结算通知 🎉

> **订单编号:** order_20250712_001
> **交易方向:** UP
> **投注金额:** 25.00 USDT
> **结算结果:** 盈利 🎉
> **盈亏金额:** +22.50 USDT 💰
> **收益率:** +90.0%
> **结算时间:** 2025-07-12 09:40:05

🎊 恭喜！**交易**成功！继续保持！🚀
```

### 3. 风险提醒

**触发条件**: 
- 当日损失超过设定比例
- 连续亏损达到阈值
- 达到风险限制

**包含内容**:
- ⚠️ 风险类型和描述
- 💸 当前损失金额和比例
- 💡 操作建议

**示例消息**:
```
### ⚠️ **交易**风险提醒

> **风险类型:** 损失超限
> **风险描述:** 当日损失已达到50%软限制
> **当前损失:** 500.00 USDT
> **损失比例:** 50.0%
> **提醒时间:** 2025-07-12 09:45:05

💡 **建议:** 关注风险，适当调整**交易**策略！

🔒 风险控制是**交易**成功的关键！
```

### 4. 每日总结

**触发条件**: 每日结束时自动发送

**包含内容**:
- 📈 交易绩效统计
- ⚠️ 风险控制情况
- 💪 总结评价

**示例消息**:
```
### 📈 **交易**每日总结报告

> **报告日期:** 2025-07-12

#### 📊 **交易**绩效
> **总交易数:** 8
> **胜率:** 62.5%
> **总盈亏:** +156.50 USDT
> **平均投注:** 22.50 USDT
> **连胜:** 3 次 🎉

#### ⚠️ 风险控制
> **当日损失:** 85.00 USDT
> **损失比例:** 8.5%
> **剩余限额:** 915.00 USDT

🎊 今日**交易**表现优秀！继续保持！🚀
```

## ⚙️ 配置说明

### NotificationConfig 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `enable_signal_notification` | bool | True | 启用交易信号通知 |
| `enable_settlement_notification` | bool | True | 启用结算通知 |
| `enable_risk_notification` | bool | True | 启用风险提醒 |
| `enable_daily_summary` | bool | True | 启用每日总结 |
| `dingtalk_token` | str | None | 钉钉Webhook URL |
| `min_signal_interval` | int | 300 | 最小信号通知间隔（秒） |
| `max_daily_notifications` | int | 50 | 每日最大通知数 |
| `risk_notification_threshold` | float | 0.5 | 风险提醒阈值 |
| `consecutive_loss_threshold` | int | 3 | 连续亏损阈值 |

### 钉钉Webhook配置

1. 在钉钉群组中添加自定义机器人
2. 获取Webhook URL
3. 配置安全设置（推荐使用关键词验证）
4. 将URL设置到`dingtalk_token`参数

## 🛡️ 安全和限制

### 频率限制
- **最小间隔**: 防止过于频繁的通知
- **每日限额**: 避免超过钉钉API限制
- **智能过滤**: 自动过滤重复或低价值通知

### 关键词验证
- 所有消息都包含"交易"关键词
- 交易信号消息包含"小火箭"🚀
- 符合钉钉机器人安全设置要求

### 错误处理
- 网络异常自动重试
- 失败通知记录日志
- 优雅降级处理

## 📊 监控和统计

### 通知统计
```python
# 获取通知统计
stats = notifier.get_notification_stats()
print(f"今日通知总数: {stats['total_today']}")
print(f"剩余配额: {stats['remaining_quota']}")
print(f"各类型通知: {stats['types_today']}")
```

### 重置计数器
```python
# 每日开始时重置计数器
notifier.reset_daily_counters()
```

## 🔧 高级用法

### 自定义消息格式
```python
# 继承并自定义消息格式
class CustomDingtalkNotifier(EventContractDingtalkNotifier):
    def _build_signal_message(self, decision, signal_result, market_data):
        # 自定义消息格式
        message = "自定义交易信号格式..."
        return message
```

### 条件通知
```python
# 只在特定条件下发送通知
if decision.confidence > 90 and decision.risk_level == RiskLevel.LOW:
    notifier.send_trading_signal(decision, signal_result, market_data)
```

## 📝 最佳实践

1. **合理设置频率限制**: 避免过于频繁的通知导致用户疲劳
2. **关键词合规**: 确保消息包含必要的关键词
3. **错误处理**: 妥善处理网络异常和API错误
4. **监控统计**: 定期检查通知发送情况
5. **测试验证**: 上线前充分测试各种场景

## 🚨 注意事项

- 钉钉Webhook有速率限制，请合理设置通知频率
- 消息内容需符合钉钉平台规范
- 建议在测试环境中验证所有通知类型
- 保护好Webhook URL，避免泄露

## 🎯 总结

钉钉通知集成为事件合约交易系统提供了完整的消息推送能力，支持：

✅ **完整的消息类型**: 交易信号、结算通知、风险提醒、每日总结  
✅ **智能频率控制**: 避免过度通知  
✅ **关键词合规**: 包含必要的关键词  
✅ **错误处理**: 优雅的异常处理  
✅ **统计监控**: 完整的使用统计  
✅ **安全可靠**: 符合钉钉平台规范  

通过合理配置和使用，可以大大提升交易系统的用户体验和监控能力。