# 币安事件合约自动化交易主策略使用指南

## 📋 概述

`EventContractMainStrategy` 是币安事件合约自动化交易系统的核心策略类，整合了所有交易组件，实现完整的自动化交易流程。

## 🎯 主要功能

### 1. 核心功能
- **信号生成**: 基于技术指标和概率分析生成交易信号
- **决策引擎**: 智能决策是否执行交易
- **自动下单**: 自动执行事件合约交易
- **结算检查**: 自动检查合约到期结果
- **风险管理**: 实时监控风险，自动停损
- **钉钉通知**: 实时发送交易信息到钉钉群

### 2. 集成组件
- `EnhancedBinanceEventContractsRestApi`: 币安API客户端
- `EventContractSignalGeneratorSimple`: 信号生成器
- `EventContractDecisionEngine`: 决策引擎
- `EventContractDingtalkNotifier`: 钉钉通知器
- `EventContractSettlementChecker`: 结算检查器
- `EventContractTradeHistoryManager`: 交易历史管理器

## 🚀 快速开始

### 1. 环境准备

确保已安装所有依赖：
```bash
pip install -r requirements.txt
```

### 2. 配置文件

确保 `config.json` 包含必要配置：
```json
{
    "PLATFORMS": {
        "binance": {
            "access_key": "your_binance_access_key",
            "secret_key": "your_binance_secret_key"
        }
    },
    "DINGTALK": "your_dingtalk_webhook_url"
}
```

### 3. 启动策略

#### 方式1：使用启动脚本
```bash
cd scripts
python run_event_contract_strategy.py
```

#### 方式2：直接运行
```bash
python -m quant.strategies.event_contract_main_strategy
```

#### 方式3：代码调用
```python
import asyncio
from quant.strategies.event_contract_main_strategy import EventContractMainStrategy

async def main():
    strategy = EventContractMainStrategy()
    await strategy.start()

asyncio.run(main())
```

## 📊 运行流程

### 1. 初始化阶段
1. 加载配置文件
2. 初始化所有组件
3. 建立数据库连接
4. 设置组件间回调

### 2. 主循环（每10秒）
1. **结算检查**: 检查待结算的合约
2. **信号生成**: 分析市场生成交易信号
3. **决策处理**: 决策引擎评估是否交易
4. **执行交易**: 自动下单并记录
5. **统计更新**: 更新每日统计数据
6. **风险检查**: 检查风险限制
7. **报告发送**: 定期发送统计报告

### 3. 交易流程
```
信号生成 → 决策评估 → 自动下单 → 钉钉通知 → 结算检查 → 结果通知
```

## ⚙️ 配置选项

### 1. 风险管理
- **软限制**: 单日亏损 -1000 USDT（发送警告）
- **硬限制**: 单日亏损 -10000 USDT（自动停止）
- **基础投注**: 20 USDT（可动态调整）

### 2. 通知频率
- **交易信号**: 每笔交易实时通知
- **结算结果**: 合约到期实时通知
- **小时报告**: 每小时发送统计报告
- **风险警告**: 触发风险限制时立即通知

### 3. 数据存储
- **交易历史**: 存储在 `./data/trade_history.db`
- **日志文件**: 存储在 `./logs/` 目录
- **统计数据**: 实时计算并持久化

## 🔧 高级配置

### 1. 自定义信号生成器
```python
# 替换默认信号生成器
strategy.signal_generator = YourCustomSignalGenerator(api_client)
```

### 2. 自定义决策引擎
```python
# 替换默认决策引擎
strategy.decision_engine = YourCustomDecisionEngine(api_client)
```

### 3. 自定义通知器
```python
# 替换默认通知器
strategy.dingtalk_notifier = YourCustomNotifier(webhook_url)
```

## 📈 监控和管理

### 1. 状态查询
```python
# 获取策略状态
status = await strategy.get_status()
print(status)
```

### 2. 手动停止
```python
# 优雅停止策略
await strategy.stop()
```

### 3. 日志监控
```bash
# 实时查看日志
tail -f logs/error.log
```

## 🛡️ 安全注意事项

### 1. API密钥安全
- 使用环境变量存储敏感信息
- 定期更换API密钥
- 限制API权限（仅交易权限）

### 2. 资金安全
- 设置合理的风险限制
- 定期检查账户余额
- 避免过度杠杆

### 3. 系统安全
- 定期备份交易数据
- 监控系统运行状态
- 及时处理异常情况

## 🐛 故障排除

### 1. 常见问题

**Q: 策略无法启动？**
A: 检查配置文件和API密钥是否正确

**Q: 没有收到钉钉通知？**
A: 检查webhook URL和网络连接

**Q: 交易信号不执行？**
A: 检查决策引擎的风险控制设置

**Q: 数据库连接失败？**
A: 检查data目录权限和磁盘空间

### 2. 调试模式
```python
# 启用调试日志
import logging
logging.basicConfig(level=logging.DEBUG)

# 创建策略实例
strategy = EventContractMainStrategy()
```

### 3. 测试模式
```bash
# 运行测试
python tests/test_main_strategy.py
```

## 📞 支持和反馈

如有问题或建议，请：
1. 查看日志文件定位问题
2. 运行测试验证功能
3. 检查配置文件设置
4. 联系技术支持

## 🔄 版本更新

### v1.0.0
- 初始版本发布
- 基础自动化交易功能
- 完整的风险管理系统
- 钉钉通知集成

---

**注意**: 本策略仅供学习和研究使用，实际交易请谨慎评估风险。 