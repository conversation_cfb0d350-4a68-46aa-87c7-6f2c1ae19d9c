"""
币安事件合约自动化交易主策略类
整合所有功能模块，实现完整的自动化交易流程
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta, date
import json
import traceback
from dataclasses import asdict

from quant.platform.enhanced_binance_event_contracts import EnhancedBinanceEventContractsAPI
# 导入 SignalResult 供类型注解使用
from quant.strategies.event_contract_signal_generator_simple import EventContractSignalGeneratorSimple, SignalResult
# === 新增导入 ===
from quant.strategies.factor_filter import FactorFilter, MinuteKline
from quant.strategies.recommendation_engine import RecommendationEngine
# =================
from quant.strategies.event_contract_dingtalk_notifier import EventContractDingtalkNotifier
from quant.strategies.event_contract_settlement_checker import EventContractSettlementChecker
from quant.strategies.event_contract_trade_history_manager import EventContractTradeHistoryManager
from quant.utils import logger
from quant.config import config
from quant.platform.binance_spot import BinanceSpotRestApi
from quant import const


class EventContractMainStrategy:
    """
    币安事件合约自动化交易主策略
    
    功能：
    1. 整合所有交易组件
    2. 实现完整的自动化交易流程
    3. 提供启动停止控制
    4. 异常处理和错误恢复
    """
    
    def __init__(self, config_path: str = "config.json"):
        """
        初始化主策略
        
        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = self._load_config()
        self.is_running = False
        self.last_check_time = None
        self.last_hourly_report = None
        
        # 初始化各个组件
        self._init_components()
        
        # 运行状态
        self.daily_stats = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'total_trades': 0,
            'total_pnl': 0.0,
            'current_balance': 0.0,
            'risk_level': 'LOW'
        }
        
        # 🆕 数据获取相关
        self.last_data_fetch = None
        self.data_fetch_interval = 60  # 每60秒获取一次数据
        
        # 🆕 K线序号跟踪
        self.daily_kline_tracker = {
            'date': datetime.now().strftime('%Y-%m-%d'),
            'kline_15m_count': 0,  # 当日15分钟K线计数
            'signal_count': 0,     # 当日信号计数
            'last_kline_time': None,  # 最后一根K线时间
            'kline_sequence': []   # K线序列记录
        }
        
        # 🆕 记录上一根已发送信号的15分钟K线序号，用于去重
        self.last_signal_slot: Optional[int] = None
        self.last_signal_date: Optional[date] = None
        
        logger.info("事件合约主策略初始化完成")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            # 加载全局配置
            config.loads(self.config_path)
            
            # 也返回配置字典供内部使用
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            raise
    
    def _init_components(self):
        """初始化所有组件"""
        try:
            # 1. API客户端
            self.api_client = EnhancedBinanceEventContractsAPI(
                access_key=self.config['PLATFORMS']['binance']['access_key'],
                secret_key=self.config['PLATFORMS']['binance']['secret_key']
            )
            
            # 🆕 添加现货API客户端用于获取K线数据
            self.spot_api = BinanceSpotRestApi(
                access_key=self.config['PLATFORMS']['binance']['access_key'],
                secret_key=self.config['PLATFORMS']['binance']['secret_key']
            )
            
            # 2. 信号生成器
            self.signal_generator = EventContractSignalGeneratorSimple()
            
            # === 删除决策引擎，改为推荐引擎 ===
            # 🔧 使用优化后的FactorFilter参数
            self.factor_filter = FactorFilter(
                threshold=25.0,  # 从40.0降低到25.0
                weights={
                    "price_action": 15,  # 从20降低到15
                    "volume": 20,        # 从15增加到20
                    "momentum": 20,      # 从15增加到20
                    "structure": 10,     # 保持不变
                    "time_decay": 0,     # 特殊处理
                }
            )
            self.recommend_engine = RecommendationEngine()

            # pending_signal 生命周期
            self.pending_signal = None  # type: Optional[SignalResult]
            self.pending_created_at = None
            # =================
            
            # 4. 钉钉通知器
            self.dingtalk_notifier = EventContractDingtalkNotifier()
            
            # 5. 结算检查器
            self.settlement_checker = EventContractSettlementChecker(
                api_client=self.api_client,
                dingtalk_notifier=self.dingtalk_notifier
            )
            
            # 6. 交易历史管理器
            self.trade_history_manager = EventContractTradeHistoryManager(
                db_path="./data/trade_history.db"
            )
            
            # 🔧 新增: 信号结算检查器
            from quant.strategies.signal_settlement_checker import SignalSettlementChecker
            self.signal_settlement_checker = SignalSettlementChecker(
                db_path="./data/signal_settlement.db"
            )
            
            # 设置结算检查器的回调
            self.settlement_checker.add_settlement_callback(
                self.trade_history_manager.update_from_contract_record
            )
            
            logger.info("所有组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {e}")
            raise
    
    async def start(self):
        """启动主策略"""
        if self.is_running:
            logger.warning("策略已在运行中")
            return
        
        self.is_running = True
        logger.info("🚀 事件合约自动化交易策略启动")
        
        # 发送启动通知
        await self._send_startup_notification()
        
        # 🆕 初始化历史数据
        await self._initialize_historical_data()
        
        try:
            # 主循环
            while self.is_running:
                await self._main_loop()
                await asyncio.sleep(10)  # 每10秒检查一次
                
        except KeyboardInterrupt:
            logger.info("收到停止信号")
        except Exception as e:
            logger.error(f"策略运行异常: {e}")
            logger.error(traceback.format_exc())
        finally:
            await self.stop()
    
    async def stop(self):
        """停止主策略"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("🛑 事件合约自动化交易策略停止")
        
        # 发送停止通知
        await self._send_shutdown_notification()
    
    async def _initialize_historical_data(self):
        """初始化历史K线数据"""
        try:
            logger.info("🔄 正在初始化历史K线数据...")
            
            # 获取最近500根1分钟K线数据（币安API默认返回500根）
            success, error = self.spot_api.get_kline("BTC/USDT", const.KLINE_1M)
            
            if success:
                logger.info(f"✅ 成功获取 {len(success)} 根1分钟K线数据")
                
                # 将历史数据输入信号生成器
                for kline in success:
                    timestamp = int(kline[0])  # 开盘时间
                    open_price = float(kline[1])
                    high_price = float(kline[2])
                    low_price = float(kline[3])
                    close_price = float(kline[4])
                    volume = float(kline[5])
                    
                    self.signal_generator.add_kline_data(
                        timestamp=timestamp,
                        open_price=open_price,
                        high_price=high_price,
                        low_price=low_price,
                        close_price=close_price,
                        volume=volume
                    )
                
                # 检查数据状态
                klines_1m = list(self.signal_generator.klines['1m'])
                klines_5m = list(self.signal_generator.klines['5m'])
                klines_15m = list(self.signal_generator.klines['15m'])
                
                logger.info(f"✅ 历史K线数据初始化完成: "
                           f"1分钟({len(klines_1m)}根), "
                           f"5分钟({len(klines_5m)}根), "
                           f"15分钟({len(klines_15m)}根)")
                
                # 如果15分钟K线数据充足，显示当前状态
                if len(klines_15m) >= 10:
                    latest_price = float(success[-1][4])
                    logger.info(f"📊 当前BTC价格: {latest_price:.2f} USDT")
                    logger.info("🎯 系统已准备好进行信号分析")
                else:
                    logger.warning(f"⚠️ 15分钟K线数据不足({len(klines_15m)}/10)，需要等待更多数据")
                    
            else:
                logger.error(f"❌ 获取历史K线数据失败: {error}")
                
        except Exception as e:
            logger.error(f"初始化历史数据异常: {e}")
    
    async def _fetch_latest_data(self):
        """获取最新数据"""
        try:
            # 获取最新1分钟K线数据
            success, error = self.spot_api.get_kline("BTC/USDT", const.KLINE_1M)
            
            if success and len(success) > 0:
                # 只使用最新的K线数据
                kline = success[-1]
                timestamp = int(kline[0])
                open_price = float(kline[1])
                high_price = float(kline[2])
                low_price = float(kline[3])
                close_price = float(kline[4])
                volume = float(kline[5])
                
                # 输入到信号生成器
                self.signal_generator.add_kline_data(
                    timestamp=timestamp,
                    open_price=open_price,
                    high_price=high_price,
                    low_price=low_price,
                    close_price=close_price,
                    volume=volume
                )
                
                logger.debug(f"📊 更新K线数据: {close_price:.2f} USDT")
                self.last_data_fetch = datetime.now()
                
            else:
                logger.warning(f"获取最新K线数据失败: {error}")
                
        except Exception as e:
            logger.error(f"获取最新数据异常: {e}")
    
    async def _main_loop(self):
        """主循环逻辑"""
        try:
            current_time = datetime.now()
            
            # 🆕 1. 定期获取最新数据
            if (self.last_data_fetch is None or 
                (current_time - self.last_data_fetch).total_seconds() >= self.data_fetch_interval):
                await self._fetch_latest_data()

            # 2. 检查是否需要结算
            await self._check_settlements()
            
            # 🔧 新增: 2.5. 检查信号结算
            await self._check_signal_settlements()

            # 3. 生成交易信号
            await self._generate_and_process_signals()

            # 3.5 评估 pending_signal → 生成最终推荐
            await self._evaluate_pending_signal()
            
            # 4. 更新每日统计
            await self._update_daily_stats()
            
            # 5. 风险检查
            await self._check_risk_limits()
            
            # 6. 定期发送统计报告（每小时）
            if self._should_send_hourly_report():
                await self._send_hourly_report()
            
        except Exception as e:
            logger.error(f"主循环异常: {e}")
            logger.error(traceback.format_exc())
    
    async def _check_settlements(self):
        """检查合约结算"""
        try:
            # 获取需要检查的合约
            pending_contracts = self.settlement_checker.get_pending_contracts()
            
            if pending_contracts:
                logger.info(f"检查 {len(pending_contracts)} 个待结算合约")
                
                # 批量检查结算
                results = await self.settlement_checker.check_settlements_batch(
                    pending_contracts
                )
                
                if results:
                    logger.info(f"完成 {len(results)} 个合约结算")
                    
        except Exception as e:
            logger.error(f"结算检查异常: {e}")

    async def _check_signal_settlements(self):
        """检查信号结算"""
        try:
            # 获取当前BTC价格
            current_price = await self._get_current_btc_price()
            if current_price is None:
                logger.warning("无法获取当前BTC价格，跳过信号结算检查")
                return
            
            # 执行信号结算检查
            settled_signals = self.signal_settlement_checker.check_and_settle_signals(current_price)
            
            if settled_signals:
                logger.info(f"✅ 完成 {len(settled_signals)} 个信号结算")
                
                # 发送结算通知
                for signal in settled_signals:
                    await self._send_signal_settlement_notification(signal)
                    
        except Exception as e:
            logger.error(f"信号结算检查异常: {e}")
    
    async def _get_current_btc_price(self) -> Optional[float]:
        """获取当前BTC价格"""
        try:
            # 获取最新K线数据
            success, error = self.spot_api.get_kline("BTC/USDT", const.KLINE_1M)
            
            if success and len(success) > 0:
                # 使用最新K线的收盘价
                latest_kline = success[-1]
                current_price = float(latest_kline[4])  # 收盘价
                return current_price
            else:
                logger.error(f"获取BTC价格失败: {error}")
                return None
                
        except Exception as e:
            logger.error(f"获取BTC价格异常: {e}")
            return None
    
    async def _send_signal_settlement_notification(self, signal: Dict):
        """发送信号结算通知"""
        try:
            result_emoji = "✅" if signal['result'] == 'WIN' else "❌" if signal['result'] == 'LOSS' else "⚖️"
            direction_emoji = "🚀" if signal['direction'] == 'UP' else "📉"
            
            price_change = signal['price_change']
            price_change_text = f"+{price_change:.2f}%" if price_change >= 0 else f"{price_change:.2f}%"
            
            message = (
                f"📊 **信号结算通知** {result_emoji}\n\n"
                f"🆔 信号ID: {signal['signal_id']}\n"
                f"📈 信号方向: {signal['direction']} {direction_emoji}\n"
                f"💰 信号价格: {signal['signal_price']:.2f} USDT\n"
                f"🎯 结算价格: {signal['settlement_price']:.2f} USDT\n"
                f"📊 价格变化: {price_change_text}\n"
                f"🏆 结算结果: **{signal['result']}**\n"
                f"🔮 预测置信度: {signal['confidence']:.1f}%\n"
                f"⏰ 结算时间: {signal['settlement_time'][:16]}\n\n"
                f"💡 此结果将用于胜率统计和策略优化"
            )
            
            await self.dingtalk_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"发送信号结算通知失败: {e}")

    async def _check_signal_settlements_backup(self):
        """检查信号结算 - 备份方法，已被上面的方法替代"""
        try:
            # 获取当前BTC价格
            current_price = await self._get_current_btc_price()
            if current_price is None:
                logger.warning("无法获取当前BTC价格，跳过信号结算检查")
                return
            
            # 调用信号结算检查器
            settled_signals = self.signal_settlement_checker.check_and_settle_signals(current_price)
            
            if settled_signals:
                logger.info(f"完成 {len(settled_signals)} 个信号的自动结算")
                
                # 统计结算结果
                wins = len([s for s in settled_signals if s.result == 'WIN'])
                losses = len([s for s in settled_signals if s.result == 'LOSS'])
                ties = len([s for s in settled_signals if s.result == 'TIE'])
                total_pnl = sum(s.pnl for s in settled_signals)
                
                logger.info(f"结算统计: 胜利 {wins}, 失败 {losses}, 平局 {ties}, 总盈亏 {total_pnl:.2f} USDT")
                
        except Exception as e:
            logger.error(f"信号结算检查异常: {e}")
    
    async def _generate_and_process_signals(self):
        """生成信号 → 存为 pending_signal，并立即推送初步提醒"""
        try:
            signal = self.signal_generator.generate_signal()

            if not (signal and signal.has_signal):
                return

            # 15m 去重
            current_time = datetime.now()
            current_slot = (current_time.hour * 4) + (current_time.minute // 15) + 1
            if self.last_signal_date == current_time.date() and self.last_signal_slot == current_slot:
                return
            self.last_signal_date = current_time.date()
            self.last_signal_slot = current_slot

            # 🔧 修复：先更新K线跟踪器，然后再获取正确的序号信息
            self._update_kline_tracker(signal)

            self.pending_signal = signal
            self.pending_created_at = current_time

            # 🔧 修复：先生成信号ID，然后在通知中显示
            signal_id = None
            if signal.signal_price > 0:  # 确保有有效的价格信息
                signal_data = {
                    'direction': signal.direction,
                    'confidence': signal.confidence,
                    'signal_price': signal.signal_price,
                    'signal_strength': signal.signal_strength,
                    'supporting_indicators': signal.supporting_indicators,
                    'market_conditions': signal.market_status
                }
                
                signal_id = self.signal_settlement_checker.add_signal_record(signal_data)
                if signal_id:
                    logger.info(f"✅ 信号已添加到结算跟踪器: {signal_id}")
                else:
                    logger.warning("⚠️ 信号添加到结算跟踪器失败")

            # 🔧 修复：获取准确的K线序号和信号计数
            kline_info = self._get_kline_sequence_info()
            
            # 🔧 修复：发送包含信号ID和价格的pending信号提醒
            self.dingtalk_notifier.send_pending_signal(
                signal_result=signal,
                market_data={
                    'kline_sequence': kline_info['current_sequence'],
                    'signal_count': kline_info['signal_count'],
                    'kline_time': current_time.strftime('%H:%M'),
                    'signal_id': signal_id,  # 添加信号ID
                    'signal_price': signal.signal_price  # 添加信号价格
                }
            )
            
            logger.info(f"✅ 生成pending信号: {signal.direction}, 置信度: {signal.confidence:.1f}%, 价格: {signal.signal_price:.2f}, ID: {signal_id}")
            
        except Exception as e:
            logger.error(f"信号生成处理异常: {e}")
            logger.error(traceback.format_exc())

    async def _evaluate_pending_signal(self):
        """每分钟检查 pending_signal，应用因子筛选并生成最终推荐"""
        if not self.pending_signal:
            return

        now = datetime.now()
        # 🔧 使用更宽松的时间限制：从900秒改为600秒
        if (now - self.pending_created_at).total_seconds() > 600:  # 10分钟超期
            logger.info(f"⏰ pending_signal已超时，清空待评估信号")
            self.pending_signal = None
            return

        # 收集最近 20 根 1m kline
        klines_1m_all = list(self.signal_generator.klines['1m'])
        klines_1m = klines_1m_all[-20:] if len(klines_1m_all) >= 20 else klines_1m_all
        minute_klines = [MinuteKline(
            timestamp=k.timestamp,
            open=k.open,
            high=k.high,
            low=k.low,
            close=k.close,
            volume=k.volume,
        ) for k in klines_1m]

        # 🔧 使用更详细的技术指标计算
        indicators = {'rsi': 50}  # TODO: 可调用计算器
        
        # 如果有足够的K线数据，尝试计算更准确的RSI
        if len(klines_1m) >= 14:
            closes = [k.close for k in klines_1m]
            try:
                # 简化的RSI计算
                deltas = [closes[i] - closes[i-1] for i in range(1, len(closes))]
                gains = [d if d > 0 else 0 for d in deltas]
                losses = [-d if d < 0 else 0 for d in deltas]
                
                if len(gains) >= 14 and len(losses) >= 14:
                    avg_gain = sum(gains[-14:]) / 14
                    avg_loss = sum(losses[-14:]) / 14
                    
                    if avg_loss != 0:
                        rs = avg_gain / avg_loss
                        rsi = 100 - (100 / (1 + rs))
                        indicators['rsi'] = rsi
                        logger.info(f"📊 计算得到RSI: {rsi:.2f}")
            except Exception as e:
                logger.warning(f"RSI计算失败，使用默认值: {e}")
        
        factor_eval = self.factor_filter.evaluate_entry(self.pending_created_at, now, minute_klines, indicators)
        
        # 记录详细的评估信息
        logger.info(f"📊 因子评估结果: 得分={factor_eval['score']:.1f}/{self.factor_filter.threshold}, "
                   f"剩余时间={factor_eval['remaining_time']}s, "
                   f"推荐入场={factor_eval['recommend_entry']}")
        
        # 记录各因子详情
        for factor_name, score in factor_eval['factors'].items():
            logger.info(f"   {factor_name}: {score}")

        recommendation = self.recommend_engine.make_recommendation(self.pending_signal, factor_eval)

        if recommendation['has_recommendation']:
            # 发送推荐并清空 pending
            logger.info(f"🚀 生成入场推荐: {recommendation}")
            self.dingtalk_notifier.send_recommendation(recommendation)
            self.pending_signal = None
        else:
            logger.info(f"⏳ 暂未满足入场条件: {factor_eval['reason']}")
    
    def _update_kline_tracker(self, signal):
        """更新K线序号跟踪器"""
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now()
            
            # 检查是否需要重置每日计数器
            if self.daily_kline_tracker['date'] != current_date:
                self.daily_kline_tracker = {
                    'date': current_date,
                    'kline_15m_count': 0,
                    'signal_count': 0,
                    'last_kline_time': None,
                    'kline_sequence': []
                }
                logger.info(f"🔄 重置K线跟踪器到新日期: {current_date}")
            
            # 🔧 修复：精确计算15分钟K线序号（基于当日时间）
            # 一天有96个15分钟K线（24小时 * 4）
            current_15m_slot = (current_time.hour * 4) + (current_time.minute // 15) + 1
            
            # 🔧 修复：检查是否是新的15分钟K线周期
            if (self.daily_kline_tracker['last_kline_time'] is None or 
                current_15m_slot != self.daily_kline_tracker.get('last_15m_slot', 0)):
                
                # 更新K线序号
                self.daily_kline_tracker['kline_15m_count'] = current_15m_slot
                self.daily_kline_tracker['last_15m_slot'] = current_15m_slot
                self.daily_kline_tracker['last_kline_time'] = current_time
                
                # 记录K线序列
                kline_info = {
                    'sequence': current_15m_slot,
                    'time': current_time.strftime('%H:%M'),
                    'timestamp': current_time.isoformat()
                }
                self.daily_kline_tracker['kline_sequence'].append(kline_info)
                
                # 保留最近100个K线记录
                if len(self.daily_kline_tracker['kline_sequence']) > 100:
                    self.daily_kline_tracker['kline_sequence'] = self.daily_kline_tracker['kline_sequence'][-100:]
            
            # 🔧 修复：正确更新信号计数
            self.daily_kline_tracker['signal_count'] += 1
            
            logger.info(f"📊 K线跟踪更新: 第{current_15m_slot}根15分钟K线, 今日第{self.daily_kline_tracker['signal_count']}个信号")
            
        except Exception as e:
            logger.error(f"更新K线跟踪器异常: {e}")
    
    def _get_kline_sequence_info(self) -> Dict[str, Any]:
        """获取K线序号信息"""
        return {
            'current_sequence': self.daily_kline_tracker['kline_15m_count'],
            'daily_count': self.daily_kline_tracker['kline_15m_count'],
            'signal_count': self.daily_kline_tracker['signal_count'],
            'kline_time': self.daily_kline_tracker['last_kline_time'].strftime('%H:%M') if self.daily_kline_tracker['last_kline_time'] else '未知',
            'date': self.daily_kline_tracker['date']
        }
    
    def get_daily_signal_history(self) -> Dict[str, Any]:
        """
        获取今日信号发送历史（按K线序号排序）
        
        Returns:
            包含今日所有信号的详细信息
        """
        try:
            # 获取钉钉通知器的通知历史
            notification_stats = self.dingtalk_notifier.get_notification_stats()
            
            # 构建今日信号历史
            today_signals = []
            
            # 🔧 修复：统计两阶段流程的所有信号类型
            for notification in self.dingtalk_notifier.notification_history:
                if (notification['type'] in ['pending_signal', 'recommendation', 'trading_signal'] and 
                    notification['timestamp'].date() == datetime.now().date()):
                    today_signals.append(notification)
            
            # 按时间排序（即K线序号排序）
            today_signals.sort(key=lambda x: x['timestamp'])
            
            # 构建返回数据
            signal_history = {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'total_signals': len(today_signals),
                'signals': [],
                'kline_tracker': self.daily_kline_tracker.copy(),
                'summary': {
                    'up_signals': 0,
                    'down_signals': 0,
                    'pending_signals': 0,
                    'recommendation_signals': 0,
                    'high_confidence': 0,
                    'medium_confidence': 0,
                    'low_confidence': 0
                }
            }
            
            # 处理每个信号
            for i, signal in enumerate(today_signals, 1):
                signal_info = {
                    'sequence': i,
                    'timestamp': signal['timestamp'].strftime('%H:%M:%S'),
                    'direction': signal['content'],
                    'kline_time': signal['timestamp'].strftime('%H:%M'),
                    'signal_type': signal['type'],
                    # 计算15分钟K线序号
                    'kline_sequence': (signal['timestamp'].hour * 4) + (signal['timestamp'].minute // 15) + 1
                }
                
                signal_history['signals'].append(signal_info)
                
                # 统计汇总
                if 'UP' in signal['content']:
                    signal_history['summary']['up_signals'] += 1
                elif 'DOWN' in signal['content']:
                    signal_history['summary']['down_signals'] += 1
                
                # 统计信号类型
                if signal['type'] == 'pending_signal':
                    signal_history['summary']['pending_signals'] += 1
                elif signal['type'] == 'recommendation':
                    signal_history['summary']['recommendation_signals'] += 1
            
            return signal_history
            
        except Exception as e:
            logger.error(f"获取今日信号历史异常: {e}")
            return {
                'date': datetime.now().strftime('%Y-%m-%d'),
                'total_signals': 0,
                'signals': [],
                'error': str(e)
            }
    
    def print_daily_signal_summary(self):
        """打印今日信号发送汇总"""
        try:
            history = self.get_daily_signal_history()
            
            print(f"\n{'='*60}")
            print(f"📊 今日信号发送汇总 - {history['date']}")
            print(f"{'='*60}")
            
            if history['total_signals'] == 0:
                print("📭 今日暂无信号发送到钉钉")
                return
            
            print(f"📈 总信号数: {history['total_signals']}")
            print(f"🚀 看涨信号: {history['summary']['up_signals']}")
            print(f"📉 看跌信号: {history['summary']['down_signals']}")
            print(f"\n📋 信号详情（按K线序号排序）:")
            print(f"{'序号':<4} {'K线序号':<8} {'时间':<8} {'方向':<6} {'钉钉发送时间':<12}")
            print("-" * 50)
            
            for signal in history['signals']:
                direction_symbol = "🚀" if "UP" in signal['direction'] else "📉"
                print(f"{signal['sequence']:<4} "
                      f"第{signal['kline_sequence']:<3}根  "
                      f"{signal['kline_time']:<8} "
                      f"{direction_symbol:<6} "
                      f"{signal['timestamp']:<12}")
            
            print(f"{'='*60}")
            
        except Exception as e:
            logger.error(f"打印今日信号汇总异常: {e}")
            print(f"❌ 无法获取今日信号汇总: {e}")
    
    async def _send_startup_notification(self):
        """发送启动通知"""
        try:
            message = (
                "🚀 **事件合约自动化交易系统启动** 🚀\n\n"
                f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"💰 初始资金: 待查询\n"
                f"🎯 交易标的: BTCUSDT事件合约\n"
                f"⚙️ 策略模式: 自动化交易\n\n"
                "系统已就绪，开始监控市场机会！ 🔍"
            )
            
            await self.dingtalk_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"发送启动通知失败: {e}")
    
    async def _send_shutdown_notification(self):
        """发送停止通知"""
        try:
            # 获取当日统计
            stats = self.trade_history_manager.get_daily_stats()
            
            message = (
                "🛑 **事件合约自动化交易系统停止** 🛑\n\n"
                f"📅 停止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"📊 今日交易: {stats.trades}笔\n"
                f"💰 今日盈亏: {stats.pnl:.2f} USDT\n"
                f"📈 今日胜率: {stats.win_rate:.1%}\n\n"
                "系统已安全停止运行。"
            )
            
            await self.dingtalk_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"发送停止通知失败: {e}")
    
    async def _send_trade_notification(self, trade_record: Dict[str, Any], trade_id: int):
        """发送交易通知"""
        try:
            direction_text = "看涨" if trade_record['direction'] == 'UP' else "看跌"
            confidence = trade_record.get('confidence', 0.5)
            
            message = (
                f"🚀 **交易信号生成** 🚀\n\n"
                f"📊 交易ID: {trade_id}\n"
                f"💰 交易标的: {trade_record['symbol']}\n"
                f"📈 方向: {direction_text}\n"
                f"💵 金额: {trade_record['amount']} USDT\n"
                f"💲 入场价: {trade_record['entry_price']:.2f}\n"
                f"🎯 信心度: {confidence:.1%}\n"
                f"⏰ 时间: {trade_record['signal_time'].strftime('%H:%M:%S')}\n\n"
                "小火箭已发射，等待结果！ 🚀"
            )
            
            await self.dingtalk_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"发送交易通知失败: {e}")
    
    async def _update_daily_stats(self):
        """更新每日统计"""
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')
            
            # 检查是否需要重置每日统计
            if self.daily_stats['date'] != current_date:
                self.daily_stats = {
                    'date': current_date,
                    'total_trades': 0,
                    'total_pnl': 0.0,
                    'current_balance': 0.0,
                    'risk_level': 'LOW'
                }
            
            # 获取最新统计
            stats = self.trade_history_manager.get_daily_stats()
            self.daily_stats.update(asdict(stats))
            
        except Exception as e:
            logger.error(f"更新每日统计失败: {e}")
    
    async def _check_risk_limits(self):
        """检查风险限制"""
        try:
            daily_pnl = self.daily_stats.get('total_pnl', 0.0)
            
            # 软限制：单日亏损1000 USDT
            if daily_pnl <= -1000:
                logger.warning(f"达到软风险限制，当日亏损: {daily_pnl:.2f} USDT")
                await self._send_risk_warning("soft", daily_pnl)
                self.daily_stats['risk_level'] = 'HIGH'
            
            # 硬限制：单日亏损10000 USDT
            if daily_pnl <= -10000:
                logger.error(f"达到硬风险限制，当日亏损: {daily_pnl:.2f} USDT")
                await self._send_risk_warning("hard", daily_pnl)
                await self.stop()  # 强制停止
            
        except Exception as e:
            logger.error(f"风险检查失败: {e}")
    
    async def _send_risk_warning(self, level: str, pnl: float):
        """发送风险警告"""
        try:
            if level == "soft":
                message = (
                    "⚠️ **风险提醒** ⚠️\n\n"
                    f"🔴 当日亏损已达到软限制\n"
                    f"💰 当前亏损: {pnl:.2f} USDT\n"
                    f"📊 限制阈值: -1000 USDT\n\n"
                    "请注意风险控制，谨慎交易！"
                )
            else:  # hard
                message = (
                    "🚨 **紧急风险警告** 🚨\n\n"
                    f"🔴 当日亏损已达到硬限制\n"
                    f"💰 当前亏损: {pnl:.2f} USDT\n"
                    f"📊 限制阈值: -10000 USDT\n\n"
                    "系统即将自动停止交易！"
                )
            
            await self.dingtalk_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"发送风险警告失败: {e}")
    
    def _should_send_hourly_report(self) -> bool:
        """判断是否应该发送小时报告"""
        current_time = datetime.now()
        
        if self.last_hourly_report is None:
            self.last_hourly_report = current_time
            return True
        
        # 每小时发送一次
        time_diff = current_time - self.last_hourly_report
        if time_diff.total_seconds() >= 3600:  # 1小时
            self.last_hourly_report = current_time
            return True
        
        return False
    
    async def _send_hourly_report(self):
        """发送小时报告"""
        try:
            # 🔧 修复：获取真实的交易统计数据
            stats = self.trade_history_manager.get_daily_stats()
            
            # 获取今日信号发送汇总
            signal_history = self.get_daily_signal_history()
            
            # 🔧 新增：获取信号结算统计
            settlement_stats = self.signal_settlement_checker.get_settlement_stats(days=7)  # 最近7天
            
            # 🔧 修复：计算信号相关的胜率（基于pending和recommendation的配对）
            pending_count = signal_history['summary']['pending_signals']
            recommendation_count = signal_history['summary']['recommendation_signals']
            
            # 计算信号触发率（有多少pending信号转化为推荐）
            signal_trigger_rate = (recommendation_count / pending_count) if pending_count > 0 else 0.0
            
            # 🔧 修复：根据实际业务逻辑显示准确信息
            message = (
                f"📊 **小时报告** 📊\n\n"
                f"⏰ 时间: {datetime.now().strftime('%H:%M')}\n"
                f"📈 今日交易: {stats.trades}笔\n"
                f"💰 今日盈亏: {stats.pnl:.2f} USDT\n"
                f"🎯 今日胜率: {stats.win_rate:.1%}\n"
                f"📊 风险等级: {self.daily_stats['risk_level']}\n\n"
                f"🔔 **信号发送统计:**\n"
                f"📡 总信号数: {signal_history['total_signals']}\n"
                f"🟡 潜在信号: {pending_count}\n"
                f"🟢 推荐信号: {recommendation_count}\n"
                f"🚀 看涨信号: {signal_history['summary']['up_signals']}\n"
                f"📉 看跌信号: {signal_history['summary']['down_signals']}\n"
                f"⚡ 信号触发率: {signal_trigger_rate:.1%}\n"
                f"📊 当前K线: 第{self.daily_kline_tracker['kline_15m_count']}/96根\n\n"
                f"🏆 **信号胜率统计** (最近7天):\n"
                f"📈 已结算信号: {settlement_stats['total_settled']}个\n"
                f"✅ 胜利信号: {settlement_stats['total_wins']}个\n"
                f"❌ 失败信号: {settlement_stats['total_losses']}个\n"
                f"⚖️ 平局信号: {settlement_stats['total_ties']}个\n"
                f"🎯 整体胜率: {settlement_stats['overall_win_rate']:.1f}%\n"
                f"💹 累计变化: {settlement_stats['total_pnl']:.2f}%\n"
                f"⏳ 待结算: {settlement_stats['pending_signals']}个\n\n"
                f"📅 **今日结算:**\n"
                f"📊 今日结算: {settlement_stats['today_stats']['settled']}个\n"
                f"🎯 今日胜率: {settlement_stats['today_stats']['win_rate']:.1f}%\n\n"
                f"💡 **系统说明:**\n"
                f"- 潜在信号: 15分钟K线收盘时生成\n"
                f"- 推荐信号: 找到最佳入场点时生成\n"
                f"- 胜率统计: 基于15分钟后的价格表现\n"
                f"- 用户需手动执行交易建议\n\n"
                "系统运行正常，继续监控中... 👀"
            )
            
            await self.dingtalk_notifier.send_message(message)
            
        except Exception as e:
            logger.error(f"发送小时报告失败: {e}")
    
    async def get_status(self) -> Dict[str, Any]:
        """获取策略状态"""
        try:
            stats = self.trade_history_manager.get_daily_stats()
            
            return {
                'is_running': self.is_running,
                'start_time': self.last_check_time,
                'daily_stats': self.daily_stats,
                'performance': stats,
                'components_status': {
                    'api_client': 'OK',
                    'signal_generator': 'OK',
                    'recommend_engine': 'OK',
                    'settlement_checker': 'OK',
                    'trade_history': 'OK',
                    'dingtalk_notifier': 'OK'
                }
            }
            
        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            return {'error': str(e)}


# 启动脚本
async def main():
    """主启动函数"""
    try:
        # 创建策略实例
        strategy = EventContractMainStrategy()
        
        # 启动策略
        await strategy.start()
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    # 运行主策略
    asyncio.run(main()) 