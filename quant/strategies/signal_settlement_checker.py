#!/usr/bin/env python3
"""
信号结算检查器 - 自动检查和结算到期的交易信号
基于历史信号的价格表现计算胜率统计
"""

import json
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
import logging

from quant.utils import logger


@dataclass
class SignalRecord:
    """信号记录数据结构"""
    signal_id: str
    timestamp: str
    direction: str  # UP/DOWN
    confidence: float
    signal_price: float
    expiry_time: str
    result: str = 'PENDING'  # PENDING/WIN/LOSS/TIE
    settlement_price: Optional[float] = None
    settlement_time: Optional[str] = None
    pnl: float = 0.0
    auto_settled: bool = False
    signal_strength: str = 'MEDIUM'
    supporting_indicators: List[str] = None
    market_conditions: str = 'NORMAL'

    def __post_init__(self):
        if self.supporting_indicators is None:
            self.supporting_indicators = []


class SignalSettlementChecker:
    """信号结算检查器 - 自动检查和结算到期的交易信号"""

    def __init__(self, db_path: str = "./data/signal_settlement.db"):
        self.db_path = db_path
        self.last_check_time = datetime.now()
        self._init_database()
        logger.info(f"信号结算检查器已初始化，数据库路径: {db_path}")

    def _init_database(self):
        """初始化数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建信号记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS signal_records (
                    signal_id TEXT PRIMARY KEY,
                    timestamp TEXT NOT NULL,
                    direction TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    signal_price REAL NOT NULL,
                    expiry_time TEXT NOT NULL,
                    result TEXT DEFAULT 'PENDING',
                    settlement_price REAL,
                    settlement_time TEXT,
                    pnl REAL DEFAULT 0.0,
                    auto_settled BOOLEAN DEFAULT FALSE,
                    signal_strength TEXT DEFAULT 'MEDIUM',
                    supporting_indicators TEXT,
                    market_conditions TEXT DEFAULT 'NORMAL',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 创建结算统计表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS settlement_stats (
                    date TEXT PRIMARY KEY,
                    total_signals INTEGER DEFAULT 0,
                    total_settled INTEGER DEFAULT 0,
                    wins INTEGER DEFAULT 0,
                    losses INTEGER DEFAULT 0,
                    ties INTEGER DEFAULT 0,
                    win_rate REAL DEFAULT 0.0,
                    avg_confidence REAL DEFAULT 0.0,
                    total_pnl REAL DEFAULT 0.0,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            conn.commit()
            conn.close()
            logger.info("信号结算数据库初始化完成")

        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise

    def add_signal_record(self, signal_data: Dict) -> str:
    """
    添加信号记录
    
    Args:
        signal_data: 信号数据，包含direction, confidence, signal_price等
        
    Returns:
        signal_id: 信号ID
    """
    try:
        # 生成唯一信号ID（包含随机数避免冲突）
        import random
        signal_id = f"signal_{int(datetime.now().timestamp()*1000)}_{random.randint(1000, 9999)}"
        
        # 🔧 修复：精确计算到期时间（严格10分钟，600秒）
        signal_time = datetime.now()
        expiry_time = signal_time + timedelta(seconds=600)  # 精确600秒
        
        # 创建信号记录
        record = SignalRecord(
            signal_id=signal_id,
            timestamp=signal_time.isoformat(),
            direction=signal_data.get('direction', 'UP'),
            confidence=signal_data.get('confidence', 50.0),
            signal_price=signal_data.get('signal_price', 0.0),
            expiry_time=expiry_time.isoformat(),
            signal_strength=signal_data.get('signal_strength', 'MEDIUM'),
            supporting_indicators=signal_data.get('supporting_indicators', []),
            market_conditions=signal_data.get('market_conditions', 'NORMAL')
        )
        
        # 保存到数据库
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO signal_records 
            (signal_id, timestamp, direction, confidence, signal_price, expiry_time,
             signal_strength, supporting_indicators, market_conditions)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            record.signal_id,
            record.timestamp,
            record.direction,
            record.confidence,
            record.signal_price,
            record.expiry_time,
            record.signal_strength,
            json.dumps(record.supporting_indicators),
            record.market_conditions
        ))
        
        conn.commit()
        conn.close()
        
        logger.info(f"📝 添加信号记录: {signal_id} | 方向: {record.direction} | 价格: {record.signal_price} | 到期时间: {expiry_time.strftime('%H:%M:%S')}")
        return signal_id
        
    except Exception as e:
        logger.error(f"添加信号记录失败: {e}")
        return None

    def check_and_settle_signals(self, current_price: float) -> List[Dict]:
        """
        检查并结算到期的交易信号

        Args:
            current_price: 当前BTC价格

        Returns:
            已结算的信号列表
        """
        current_time = datetime.now()
        settled_signals = []

        try:
            # 查找所有待结算的信号
            pending_signals = self._get_pending_signals()
            
            for signal in pending_signals:
                # 检查是否到期
                if self._is_signal_expired(signal, current_time):
                    # 执行结算
                    settlement_result = self._settle_signal(signal, current_price, current_time)
                    if settlement_result:
                        settled_signals.append(settlement_result)
                        logger.info(f"🎯 自动结算信号: {signal['signal_id']} | 结果: {settlement_result['result']} | 胜率影响: {settlement_result['result']}")

            self.last_check_time = current_time
            
            # 更新每日统计
            if settled_signals:
                self._update_daily_stats(settled_signals)
                
            return settled_signals

        except Exception as e:
            logger.error(f"信号结算检查失败: {e}")
            return []

    def _get_pending_signals(self) -> List[Dict]:
        """获取待结算的信号"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT signal_id, timestamp, direction, confidence, signal_price, expiry_time,
                       signal_strength, supporting_indicators, market_conditions
                FROM signal_records 
                WHERE result = 'PENDING'
                ORDER BY timestamp ASC
            ''')
            
            results = cursor.fetchall()
            conn.close()
            
            signals = []
            for row in results:
                signals.append({
                    'signal_id': row[0],
                    'timestamp': row[1],
                    'direction': row[2],
                    'confidence': row[3],
                    'signal_price': row[4],
                    'expiry_time': row[5],
                    'signal_strength': row[6],
                    'supporting_indicators': json.loads(row[7]) if row[7] else [],
                    'market_conditions': row[8]
                })
            
            return signals
            
        except Exception as e:
            logger.error(f"获取待结算信号失败: {e}")
            return []

    def _is_signal_expired(self, signal: Dict, current_time: datetime) -> bool:
    """检查信号是否已到期"""
    try:
        expiry_time = datetime.fromisoformat(signal['expiry_time'].replace('Z', '+00:00'))
        
        # 🔧 修复：严格按照10分钟到期时间，不允许提前结算
        # 移除30秒缓冲时间，确保信号必须满足完整的10分钟期限
        is_expired = current_time >= expiry_time
        
        if is_expired:
            # 记录实际的时间间隔
            signal_time = datetime.fromisoformat(signal['timestamp'].replace('Z', '+00:00'))
            actual_duration = (current_time - signal_time).total_seconds()
            expected_duration = (expiry_time - signal_time).total_seconds()
            
            logger.info(f"🕐 信号 {signal['signal_id']} 到期检查: "
                       f"实际持续 {actual_duration:.0f}秒, 预期持续 {expected_duration:.0f}秒")
            
            # 如果实际持续时间少于580秒(9分40秒)，记录警告
            if actual_duration < 580:
                logger.warning(f"⚠️ 信号 {signal['signal_id']} 结算时间过短: {actual_duration:.0f}秒 < 580秒")
                # 但仍然允许结算，避免丢失信号
        
        return is_expired
        
    except Exception as e:
        logger.error(f"解析到期时间失败: {e}, signal: {signal.get('signal_id')}")
        return False

    def _settle_signal(self, signal: Dict, current_price: float, settlement_time: datetime) -> Dict:
        """
        结算单个信号

        Args:
            signal: 信号记录
            current_price: 当前价格
            settlement_time: 结算时间

        Returns:
            结算结果字典
        """
        try:
            signal_price = signal['signal_price']
            direction = signal['direction']
            signal_id = signal['signal_id']

            # 判断胜负
            if direction == 'UP':
                if current_price > signal_price:
                    result = 'WIN'
                elif current_price < signal_price:
                    result = 'LOSS'
                else:
                    result = 'TIE'
            elif direction == 'DOWN':
                if current_price < signal_price:
                    result = 'WIN'
                elif current_price > signal_price:
                    result = 'LOSS'
                else:
                    result = 'TIE'
            else:
                logger.error(f"未知的信号方向: {direction}")
                return None

            # 计算价格变化幅度
            price_change = ((current_price - signal_price) / signal_price) * 100
            
            # 更新数据库中的信号记录
            self._update_signal_result(
                signal_id=signal_id,
                result=result,
                settlement_price=current_price,
                settlement_time=settlement_time.isoformat(),
                pnl=price_change
            )

            return {
                'signal_id': signal_id,
                'direction': direction,
                'signal_price': signal_price,
                'settlement_price': current_price,
                'price_change': price_change,
                'result': result,
                'confidence': signal['confidence'],
                'settlement_time': settlement_time.isoformat(),
                'auto_settled': True
            }

        except Exception as e:
            logger.error(f"结算信号失败: {e}, signal: {signal.get('signal_id')}")
            return None

    def _update_signal_result(self, signal_id: str, result: str, settlement_price: float, 
                             settlement_time: str, pnl: float):
        """更新信号结果"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('''
                UPDATE signal_records 
                SET result = ?, settlement_price = ?, settlement_time = ?, 
                    pnl = ?, auto_settled = TRUE
                WHERE signal_id = ?
            ''', (result, settlement_price, settlement_time, pnl, signal_id))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"更新信号结果失败: {e}")

    def _update_daily_stats(self, settled_signals: List[Dict]):
        """更新每日统计数据"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            
            # 统计今日结算的信号
            wins = len([s for s in settled_signals if s['result'] == 'WIN'])
            losses = len([s for s in settled_signals if s['result'] == 'LOSS'])
            ties = len([s for s in settled_signals if s['result'] == 'TIE'])
            total_settled = len(settled_signals)
            
            # 计算平均置信度
            avg_confidence = sum(s['confidence'] for s in settled_signals) / total_settled if total_settled > 0 else 0
            
            # 计算总盈亏（价格变化幅度）
            total_pnl = sum(s['price_change'] for s in settled_signals)
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查今日记录是否存在
            cursor.execute('SELECT * FROM settlement_stats WHERE date = ?', (today,))
            existing = cursor.fetchone()
            
            if existing:
                # 更新现有记录
                cursor.execute('''
                    UPDATE settlement_stats 
                    SET total_settled = total_settled + ?, 
                        wins = wins + ?, 
                        losses = losses + ?, 
                        ties = ties + ?,
                        win_rate = CASE WHEN (wins + losses) > 0 THEN (wins * 100.0 / (wins + losses)) ELSE 0 END,
                        avg_confidence = ((avg_confidence * (total_settled - ?)) + ?) / total_settled,
                        total_pnl = total_pnl + ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE date = ?
                ''', (total_settled, wins, losses, ties, total_settled, avg_confidence * total_settled, total_pnl, today))
            else:
                # 创建新记录
                win_rate = (wins / (wins + losses) * 100) if (wins + losses) > 0 else 0
                cursor.execute('''
                    INSERT INTO settlement_stats 
                    (date, total_signals, total_settled, wins, losses, ties, win_rate, avg_confidence, total_pnl)
                    VALUES (?, 0, ?, ?, ?, ?, ?, ?, ?)
                ''', (today, total_settled, wins, losses, ties, win_rate, avg_confidence, total_pnl))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"更新每日统计失败: {e}")

    def get_pending_signals_count(self) -> int:
        """获取待结算信号数量"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute('SELECT COUNT(*) FROM signal_records WHERE result = "PENDING"')
            count = cursor.fetchone()[0]
            
            conn.close()
            return count
            
        except Exception as e:
            logger.error(f"获取待结算信号数量失败: {e}")
            return 0

    def get_settlement_stats(self, days: int = 30) -> Dict:
        """获取结算统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取指定天数内的统计
            start_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            cursor.execute('''
                SELECT 
                    SUM(total_settled) as total_settled,
                    SUM(wins) as total_wins,
                    SUM(losses) as total_losses,
                    SUM(ties) as total_ties,
                    AVG(win_rate) as avg_win_rate,
                    SUM(total_pnl) as total_pnl
                FROM settlement_stats 
                WHERE date >= ?
            ''', (start_date,))
            
            result = cursor.fetchone()
            
            # 获取今日统计
            today = datetime.now().strftime('%Y-%m-%d')
            cursor.execute('''
                SELECT total_settled, wins, losses, ties, win_rate, total_pnl
                FROM settlement_stats 
                WHERE date = ?
            ''', (today,))
            
            today_stats = cursor.fetchone()
            
            conn.close()
            
            # 处理结果
            total_settled = result[0] or 0
            total_wins = result[1] or 0
            total_losses = result[2] or 0
            total_ties = result[3] or 0
            avg_win_rate = result[4] or 0
            total_pnl = result[5] or 0
            
            # 重新计算整体胜率
            overall_win_rate = (total_wins / (total_wins + total_losses) * 100) if (total_wins + total_losses) > 0 else 0
            
            return {
                'period_days': days,
                'total_settled': int(total_settled),
                'total_wins': int(total_wins),
                'total_losses': int(total_losses),
                'total_ties': int(total_ties),
                'overall_win_rate': round(overall_win_rate, 2),
                'avg_daily_win_rate': round(avg_win_rate, 2),
                'total_pnl': round(total_pnl, 2),
                'pending_signals': self.get_pending_signals_count(),
                'last_check_time': self.last_check_time.isoformat(),
                'today_stats': {
                    'settled': today_stats[0] if today_stats else 0,
                    'wins': today_stats[1] if today_stats else 0,
                    'losses': today_stats[2] if today_stats else 0,
                    'ties': today_stats[3] if today_stats else 0,
                    'win_rate': today_stats[4] if today_stats else 0,
                    'pnl': today_stats[5] if today_stats else 0
                }
            }
            
        except Exception as e:
            logger.error(f"获取结算统计失败: {e}")
            return {
                'period_days': days,
                'total_settled': 0,
                'total_wins': 0,
                'total_losses': 0,
                'total_ties': 0,
                'overall_win_rate': 0,
                'avg_daily_win_rate': 0,
                'total_pnl': 0,
                'pending_signals': 0,
                'last_check_time': datetime.now().isoformat(),
                'today_stats': {'settled': 0, 'wins': 0, 'losses': 0, 'ties': 0, 'win_rate': 0, 'pnl': 0}
            }

    def get_historical_win_rates(self, direction: str = None, strength: str = None, 
                                lookback_days: int = 30) -> Dict:
        """
        获取历史胜率数据，用于胜率预测模型校准

        Args:
            direction: 信号方向过滤 ('UP', 'DOWN', None表示所有)
            strength: 信号强度过滤 ('STRONG', 'MEDIUM', 'WEAK', None表示所有)
            lookback_days: 回看天数

        Returns:
            包含历史胜率统计的字典
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 构建查询条件
            conditions = ["result IN ('WIN', 'LOSS', 'TIE')", "datetime(timestamp) >= datetime('now', '-{} days')".format(lookback_days)]
            params = []
            
            if direction:
                conditions.append("direction = ?")
                params.append(direction)
            
            if strength:
                conditions.append("signal_strength = ?")
                params.append(strength)
            
            where_clause = " AND ".join(conditions)
            
            cursor.execute(f'''
                SELECT 
                    COUNT(*) as total_signals,
                    SUM(CASE WHEN result = 'WIN' THEN 1 ELSE 0 END) as wins,
                    SUM(CASE WHEN result = 'LOSS' THEN 1 ELSE 0 END) as losses,
                    SUM(CASE WHEN result = 'TIE' THEN 1 ELSE 0 END) as ties,
                    AVG(confidence) as avg_confidence,
                    AVG(pnl) as avg_pnl
                FROM signal_records 
                WHERE {where_clause}
            ''', params)
            
            result = cursor.fetchone()
            conn.close()
            
            total_signals = result[0] or 0
            wins = result[1] or 0
            losses = result[2] or 0
            ties = result[3] or 0
            avg_confidence = result[4] or 0
            avg_pnl = result[5] or 0
            
            # 计算胜率（不包括平局）
            decisive_trades = wins + losses
            win_rate = (wins / decisive_trades * 100) if decisive_trades > 0 else 0
            
            return {
                'total_signals': total_signals,
                'wins': wins,
                'losses': losses,
                'ties': ties,
                'win_rate': round(win_rate, 2),
                'avg_confidence': round(avg_confidence, 2),
                'avg_pnl': round(avg_pnl, 2),
                'direction': direction,
                'strength': strength,
                'lookback_days': lookback_days
            }
            
        except Exception as e:
            logger.error(f"获取历史胜率失败: {e}")
            return {
                'total_signals': 0,
                'wins': 0,
                'losses': 0,
                'ties': 0,
                'win_rate': 0,
                'avg_confidence': 0,
                'avg_pnl': 0,
                'direction': direction,
                'strength': strength,
                'lookback_days': lookback_days
            }

    def export_settlement_data(self, output_file: str = None) -> str:
        """导出结算数据到JSON文件"""
        try:
            if output_file is None:
                output_file = f"./exports/signal_settlement_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 获取所有信号记录
            cursor.execute('''
                SELECT signal_id, timestamp, direction, confidence, signal_price, expiry_time,
                       result, settlement_price, settlement_time, pnl, auto_settled,
                       signal_strength, supporting_indicators, market_conditions
                FROM signal_records 
                ORDER BY timestamp DESC
            ''')
            
            records = cursor.fetchall()
            
            # 获取统计数据
            cursor.execute('SELECT * FROM settlement_stats ORDER BY date DESC')
            stats = cursor.fetchall()
            
            conn.close()
            
            # 格式化数据
            export_data = {
                'export_time': datetime.now().isoformat(),
                'total_records': len(records),
                'signal_records': [],
                'daily_stats': []
            }
            
            # 处理信号记录
            for record in records:
                export_data['signal_records'].append({
                    'signal_id': record[0],
                    'timestamp': record[1],
                    'direction': record[2],
                    'confidence': record[3],
                    'signal_price': record[4],
                    'expiry_time': record[5],
                    'result': record[6],
                    'settlement_price': record[7],
                    'settlement_time': record[8],
                    'pnl': record[9],
                    'auto_settled': record[10],
                    'signal_strength': record[11],
                    'supporting_indicators': json.loads(record[12]) if record[12] else [],
                    'market_conditions': record[13]
                })
            
            # 处理统计数据
            for stat in stats:
                export_data['daily_stats'].append({
                    'date': stat[0],
                    'total_signals': stat[1],
                    'total_settled': stat[2],
                    'wins': stat[3],
                    'losses': stat[4],
                    'ties': stat[5],
                    'win_rate': stat[6],
                    'avg_confidence': stat[7],
                    'total_pnl': stat[8],
                    'updated_at': stat[9]
                })
            
            # 保存到文件
            import os
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"信号结算数据已导出到: {output_file}")
            return output_file
            
        except Exception as e:
            logger.error(f"导出结算数据失败: {e}")
            return None