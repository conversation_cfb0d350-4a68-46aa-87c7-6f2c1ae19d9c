"""
币安事件合约结算检查器
自动检查10分钟到期的合约结果，统计胜率和盈亏
"""
import asyncio
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any, Callable
from datetime import datetime, timedelta
from collections import defaultdict
import json
import logging

# 尝试导入自定义logger，如果不存在则使用标准logger
try:
    from quant.utils.logger import get_logger
    logger = get_logger(__name__)
except ImportError:
    import logging
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)

from .event_contract_decision_engine import TradingDecision
from .event_contract_dingtalk_notifier import EventContractDingtalkNotifier


@dataclass
class ContractRecord:
    """合约记录"""
    order_id: str                     # 订单ID
    symbol: str                       # 交易对
    direction: str                    # 方向 (UP/DOWN)
    bet_amount: float                 # 投注金额
    predicted_price: float            # 预测价格
    entry_time: datetime              # 下单时间
    expiry_time: datetime             # 到期时间
    decision: TradingDecision         # 原始决策
    
    # 结算相关
    is_settled: bool = False          # 是否已结算
    settlement_time: Optional[datetime] = None  # 结算时间
    final_price: Optional[float] = None  # 最终价格
    result: Optional[str] = None      # 结果 (win/loss)
    pnl: Optional[float] = None       # 盈亏
    return_rate: Optional[float] = None  # 收益率
    
    # 附加信息
    confidence: float = 0.0           # 原始信心度
    market_condition: str = ""        # 市场条件
    
    def is_expired(self) -> bool:
        """检查是否已到期"""
        return datetime.now() >= self.expiry_time
    
    def time_to_expiry(self) -> float:
        """距离到期时间（秒）"""
        if self.is_expired():
            return 0.0
        return (self.expiry_time - datetime.now()).total_seconds()


@dataclass
class SettlementResult:
    """结算结果"""
    order_id: str
    symbol: str
    direction: str
    bet_amount: float
    predicted_price: float
    final_price: float
    result: str  # "win" or "loss"
    pnl: float
    return_rate: float
    entry_time: datetime
    settlement_time: datetime
    confidence: float
    market_condition: str


@dataclass
class TradingStatistics:
    """交易统计"""
    total_trades: int = 0
    wins: int = 0
    losses: int = 0
    ties: int = 0  # 新增：平局次数
    win_rate: float = 0.0
    total_pnl: float = 0.0
    total_invested: float = 0.0
    avg_bet_amount: float = 0.0
    avg_win_amount: float = 0.0
    avg_loss_amount: float = 0.0
    max_consecutive_wins: int = 0
    max_consecutive_losses: int = 0
    current_streak: int = 0  # 正数为连胜，负数为连败
    
    # 按时间统计
    daily_stats: Dict[str, Dict] = None
    weekly_stats: Dict[str, Dict] = None
    monthly_stats: Dict[str, Dict] = None
    
    def __post_init__(self):
        if self.daily_stats is None:
            self.daily_stats = {}
        if self.weekly_stats is None:
            self.weekly_stats = {}
        if self.monthly_stats is None:
            self.monthly_stats = {}


class EventContractSettlementChecker:
    """事件合约结算检查器"""
    
    def __init__(self, 
                 api_client=None,
                 dingtalk_notifier: Optional[EventContractDingtalkNotifier] = None,
                 check_interval: int = 30):
        """
        初始化结算检查器
        
        Args:
            api_client: 币安API客户端
            dingtalk_notifier: 钉钉通知器
            check_interval: 检查间隔（秒）
        """
        self.api_client = api_client
        self.dingtalk_notifier = dingtalk_notifier
        self.check_interval = check_interval
        
        # 合约记录
        self.pending_contracts: Dict[str, ContractRecord] = {}  # 待结算合约
        self.settled_contracts: Dict[str, ContractRecord] = {}  # 已结算合约
        
        # 统计数据
        self.statistics = TradingStatistics()
        
        # 运行状态
        self.is_running = False
        self.check_task = None
        
        # 回调函数
        self.settlement_callbacks: List[Callable] = []
        
        logger.info(f"事件合约结算检查器已初始化，检查间隔: {check_interval}秒")
    
    def add_contract(self, 
                    order_id: str,
                    symbol: str,
                    direction: str,
                    bet_amount: float,
                    predicted_price: float,
                    decision: TradingDecision,
                    expiry_minutes: int = 10) -> None:
        """
        添加待结算合约
        
        Args:
            order_id: 订单ID
            symbol: 交易对
            direction: 方向
            bet_amount: 投注金额
            predicted_price: 预测价格
            decision: 原始决策
            expiry_minutes: 到期时间（分钟）
        """
        entry_time = datetime.now()
        expiry_time = entry_time + timedelta(minutes=expiry_minutes)
        
        contract = ContractRecord(
            order_id=order_id,
            symbol=symbol,
            direction=direction,
            bet_amount=bet_amount,
            predicted_price=predicted_price,
            entry_time=entry_time,
            expiry_time=expiry_time,
            decision=decision,
            confidence=decision.confidence,
            market_condition=str(decision.market_condition.value) if hasattr(decision.market_condition, 'value') else str(decision.market_condition)
        )
        
        self.pending_contracts[order_id] = contract
        logger.info(f"添加待结算合约: {order_id} - {direction} - {bet_amount} USDT - 到期时间: {expiry_time}")
    
    def start_monitoring(self) -> None:
        """开始监控"""
        if self.is_running:
            logger.warning("结算检查器已在运行")
            return
        
        self.is_running = True
        self.check_task = asyncio.create_task(self._monitoring_loop())
        logger.info("结算检查器开始监控")
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self.is_running:
            return
        
        self.is_running = False
        if self.check_task:
            self.check_task.cancel()
        logger.info("结算检查器停止监控")
    
    async def _monitoring_loop(self) -> None:
        """监控循环"""
        while self.is_running:
            try:
                await self._check_expired_contracts()
                await asyncio.sleep(self.check_interval)
            except asyncio.CancelledError:
                logger.info("监控循环被取消")
                break
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                await asyncio.sleep(self.check_interval)
    
    async def _check_expired_contracts(self) -> None:
        """检查到期合约"""
        expired_contracts = []
        
        for order_id, contract in self.pending_contracts.items():
            if contract.is_expired() and not contract.is_settled:
                expired_contracts.append(order_id)
        
        for order_id in expired_contracts:
            await self._settle_contract(order_id)
    
    async def _settle_contract(self, order_id: str) -> None:
        """结算合约"""
        contract = self.pending_contracts.get(order_id)
        if not contract:
            logger.warning(f"未找到合约记录: {order_id}")
            return
        
        try:
            # 获取最终价格
            final_price = await self._get_final_price(contract)
            
            # 判断结果
            result = self._determine_result(contract, final_price)
            
            # 计算盈亏
            pnl = self._calculate_pnl(contract, result)
            
            # 更新合约记录
            contract.is_settled = True
            contract.settlement_time = datetime.now()
            contract.final_price = final_price
            contract.result = result
            contract.pnl = pnl
            contract.return_rate = (pnl / contract.bet_amount) if contract.bet_amount > 0 else 0.0
            
            # 移动到已结算列表
            self.settled_contracts[order_id] = contract
            del self.pending_contracts[order_id]
            
            # 更新统计
            self._update_statistics(contract)
            
            # 发送通知
            await self._send_settlement_notification(contract)
            
            # 执行回调
            await self._execute_callbacks(contract)
            
            logger.info(f"合约结算完成: {order_id} - {result} - PnL: {pnl:.2f} USDT")
            
        except Exception as e:
            logger.error(f"结算合约异常: {order_id} - {e}")
    
    async def _get_final_price(self, contract: ContractRecord) -> float:
        """获取最终价格"""
        if self.api_client:
            try:
                # 调用API获取当前价格
                price_data = await self.api_client.get_current_price(contract.symbol)
                return float(price_data['price'])
            except Exception as e:
                logger.error(f"获取价格失败: {e}")
                # 如果API调用失败，使用模拟价格
                return self._simulate_final_price(contract)
        else:
            # 如果没有API客户端，使用模拟价格
            return self._simulate_final_price(contract)
    
    def _simulate_final_price(self, contract: ContractRecord) -> float:
        """模拟最终价格（用于测试）"""
        import random
        
        # 基于预测价格和随机波动生成最终价格
        volatility = 0.005  # 0.5%波动
        change_rate = random.uniform(-volatility, volatility)
        final_price = contract.predicted_price * (1 + change_rate)
        
        logger.info(f"模拟最终价格: {contract.symbol} - 预测: {contract.predicted_price:.2f} - 实际: {final_price:.2f}")
        return final_price
    
    def _determine_result(self, contract: ContractRecord, final_price: float) -> str:
        """判断结果"""
        # 处理价格相等的情况（平局）
        if final_price == contract.predicted_price:
            return "tie"
        
        if contract.direction == "UP":
            return "win" if final_price > contract.predicted_price else "loss"
        elif contract.direction == "DOWN":
            return "win" if final_price < contract.predicted_price else "loss"
        else:
            return "loss"
    
    def _calculate_pnl(self, contract: ContractRecord, result: str) -> float:
        """
        计算盈亏（根据币安事件合约实际规则）
        - 预测正确：获得80%收益
        - 预测错误：损失全部投入
        - 价格相等：回本（盈亏为0）
        """
        if result == "win":
            # 预测正确，获得80%收益
            return contract.bet_amount * 0.8
        elif result == "tie":
            # 价格相等，回本
            return 0.0
        else:
            # 预测错误，损失全部投入
            return -contract.bet_amount
    
    def _update_statistics(self, contract: ContractRecord) -> None:
        """更新统计数据"""
        stats = self.statistics
        
        # 基本统计
        stats.total_trades += 1
        stats.total_invested += contract.bet_amount
        stats.total_pnl += contract.pnl
        
        if contract.result == "win":
            stats.wins += 1
            if stats.current_streak >= 0:
                stats.current_streak += 1
            else:
                stats.current_streak = 1
            stats.max_consecutive_wins = max(stats.max_consecutive_wins, stats.current_streak)
        elif contract.result == "tie":
            stats.ties += 1
            # 平局不影响连胜连败记录
        else:
            stats.losses += 1
            if stats.current_streak <= 0:
                stats.current_streak -= 1
            else:
                stats.current_streak = -1
            stats.max_consecutive_losses = max(stats.max_consecutive_losses, abs(stats.current_streak))
        
        # 计算率
        stats.win_rate = stats.wins / stats.total_trades if stats.total_trades > 0 else 0.0
        stats.avg_bet_amount = stats.total_invested / stats.total_trades if stats.total_trades > 0 else 0.0
        
        # 平均盈亏
        if stats.wins > 0:
            win_amounts = [c.pnl for c in self.settled_contracts.values() if c.result == "win"]
            stats.avg_win_amount = sum(win_amounts) / len(win_amounts) if win_amounts else 0.0
        
        if stats.losses > 0:
            loss_amounts = [abs(c.pnl) for c in self.settled_contracts.values() if c.result == "loss"]
            stats.avg_loss_amount = sum(loss_amounts) / len(loss_amounts) if loss_amounts else 0.0
        
        # 按时间统计
        self._update_time_based_stats(contract)
        
        logger.info(f"统计更新: 总交易 {stats.total_trades}, 胜/负/平 {stats.wins}/{stats.losses}/{stats.ties}, 胜率 {stats.win_rate:.1%}, 总盈亏 {stats.total_pnl:.2f} USDT")
    
    def _update_time_based_stats(self, contract: ContractRecord) -> None:
        """更新基于时间的统计"""
        settlement_date = contract.settlement_time.strftime('%Y-%m-%d')
        
        # 日统计
        if settlement_date not in self.statistics.daily_stats:
            self.statistics.daily_stats[settlement_date] = {
                'trades': 0, 'wins': 0, 'losses': 0, 'ties': 0, 'pnl': 0.0, 'invested': 0.0
            }
        
        daily = self.statistics.daily_stats[settlement_date]
        daily['trades'] += 1
        daily['pnl'] += contract.pnl
        daily['invested'] += contract.bet_amount
        
        if contract.result == "win":
            daily['wins'] += 1
        elif contract.result == "tie":
            daily['ties'] += 1
        else:
            daily['losses'] += 1
        
        daily['win_rate'] = daily['wins'] / daily['trades'] if daily['trades'] > 0 else 0.0
    
    async def _send_settlement_notification(self, contract: ContractRecord) -> None:
        """发送结算通知"""
        if self.dingtalk_notifier:
            try:
                await asyncio.to_thread(
                    self.dingtalk_notifier.send_settlement_notification,
                    contract.order_id,
                    contract.result,
                    contract.pnl,
                    contract.decision
                )
            except Exception as e:
                logger.error(f"发送结算通知失败: {e}")
    
    async def _execute_callbacks(self, contract: ContractRecord) -> None:
        """执行回调函数"""
        for callback in self.settlement_callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(contract)
                else:
                    callback(contract)
            except Exception as e:
                logger.error(f"执行回调函数失败: {e}")
    
    def add_settlement_callback(self, callback: Callable) -> None:
        """添加结算回调函数"""
        self.settlement_callbacks.append(callback)
        logger.info(f"添加结算回调函数: {callback.__name__}")
    
    def get_statistics(self) -> TradingStatistics:
        """获取交易统计"""
        return self.statistics
    
    def get_daily_stats(self, date: str = None) -> Dict:
        """获取每日统计"""
        if date is None:
            date = datetime.now().strftime('%Y-%m-%d')
        
        return self.statistics.daily_stats.get(date, {
            'trades': 0, 'wins': 0, 'losses': 0, 'ties': 0, 'win_rate': 0.0, 'pnl': 0.0, 'invested': 0.0
        })
    
    def get_pending_contracts(self) -> Dict[str, ContractRecord]:
        """获取待结算合约"""
        return self.pending_contracts.copy()
    
    def get_settled_contracts(self, limit: int = 100) -> List[ContractRecord]:
        """获取已结算合约"""
        contracts = list(self.settled_contracts.values())
        contracts.sort(key=lambda x: x.settlement_time, reverse=True)
        return contracts[:limit]
    
    def export_trading_history(self, filepath: str = None) -> str:
        """导出交易历史"""
        if filepath is None:
            filepath = f"trading_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        data = {
            'statistics': {
                'total_trades': self.statistics.total_trades,
                'wins': self.statistics.wins,
                'losses': self.statistics.losses,
                'ties': self.statistics.ties,
                'win_rate': self.statistics.win_rate,
                'total_pnl': self.statistics.total_pnl,
                'total_invested': self.statistics.total_invested,
                'avg_bet_amount': self.statistics.avg_bet_amount,
                'current_streak': self.statistics.current_streak,
                'daily_stats': self.statistics.daily_stats
            },
            'settled_contracts': [
                {
                    'order_id': c.order_id,
                    'symbol': c.symbol,
                    'direction': c.direction,
                    'bet_amount': c.bet_amount,
                    'predicted_price': c.predicted_price,
                    'final_price': c.final_price,
                    'result': c.result,
                    'pnl': c.pnl,
                    'return_rate': c.return_rate,
                    'entry_time': c.entry_time.isoformat(),
                    'settlement_time': c.settlement_time.isoformat(),
                    'confidence': c.confidence,
                    'market_condition': c.market_condition
                } for c in self.settled_contracts.values()
            ]
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        logger.info(f"交易历史已导出到: {filepath}")
        return filepath
    
    def reset_statistics(self) -> None:
        """重置统计数据"""
        self.statistics = TradingStatistics()
        self.settled_contracts.clear()
        logger.info("统计数据已重置")
    
    def get_status(self) -> Dict:
        """获取状态信息"""
        return {
            'is_running': self.is_running,
            'pending_contracts': len(self.pending_contracts),
            'settled_contracts': len(self.settled_contracts),
            'check_interval': self.check_interval,
            'statistics': {
                'total_trades': self.statistics.total_trades,
                'win_rate': self.statistics.win_rate,
                'total_pnl': self.statistics.total_pnl,
                'current_streak': self.statistics.current_streak
            }
        } 