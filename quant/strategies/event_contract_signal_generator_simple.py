#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版事件合约信号生成器

专门针对币安事件合约的信号生成系统，基于技术分析预测15分钟K线方向
移除了logger依赖，方便快速测试

Author: HertelQuant Enhanced
Date: 2025-01-31
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass

# 尝试导入talib，如果不可用则使用简化版
try:
    import talib
    TALIB_AVAILABLE = True
    print("✅ talib库可用")
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ talib库不可用，使用简化版技术指标")
    from .technical_indicators_simple import (
        calculate_sma, calculate_ema, calculate_rsi, calculate_macd,
        calculate_bollinger_bands, calculate_stochastic, calculate_atr, calculate_volatility
    )


@dataclass
class KlineData:
    """K线数据结构"""
    timestamp: int
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    @property
    def is_bullish(self) -> bool:
        """判断是否为阳线：收盘价 > 开盘价"""
        return self.close > self.open
    
    @property
    def is_bearish(self) -> bool:
        """判断是否为阴线：收盘价 < 开盘价"""
        return self.close < self.open
    
    @property
    def body_size(self) -> float:
        """K线实体大小"""
        return abs(self.close - self.open)
    
    @property
    def upper_shadow(self) -> float:
        """上影线长度"""
        return self.high - max(self.open, self.close)
    
    @property
    def lower_shadow(self) -> float:
        """下影线长度"""
        return min(self.open, self.close) - self.low


@dataclass
class TechnicalIndicators:
    """技术指标数据结构"""
    # 趋势指标
    sma_5: float = 0.0
    sma_10: float = 0.0
    sma_20: float = 0.0
    ema_5: float = 0.0
    ema_10: float = 0.0
    ema_20: float = 0.0
    
    # 振荡指标
    rsi_14: float = 50.0
    rsi_21: float = 50.0
    stoch_k: float = 50.0
    stoch_d: float = 50.0
    
    # MACD指标
    macd_line: float = 0.0
    macd_signal: float = 0.0
    macd_histogram: float = 0.0
    
    # 布林带
    bb_upper: float = 0.0
    bb_middle: float = 0.0
    bb_lower: float = 0.0
    bb_position: float = 0.5  # 价格在布林带中的位置 (0-1)
    
    # KDJ指标
    kdj_k: float = 50.0
    kdj_d: float = 50.0
    kdj_j: float = 50.0
    
    # 成交量指标
    volume_sma: float = 0.0
    volume_ratio: float = 1.0
    
    # 波动率
    atr: float = 0.0
    volatility: float = 0.0


@dataclass
class SignalResult:
    """信号生成结果"""
    has_signal: bool = False
    direction: str = "NEUTRAL"  # UP, DOWN, NEUTRAL
    confidence: float = 0.0
    bullish_probability: float = 50.0
    bearish_probability: float = 50.0
    supporting_timeframes: List[str] = None
    technical_score: float = 0.0
    risk_level: str = "MEDIUM"  # LOW, MEDIUM, HIGH
    timeframe_analysis: Dict[str, Dict] = None
    generated_at: str = ""
    # 🆕 新增：用户提醒信息
    user_reminder: str = ""
    market_status: str = ""  # 市场状态：正常监控、数据不足、质量过滤、无明确机会
    
    def __post_init__(self):
        if self.supporting_timeframes is None:
            self.supporting_timeframes = []
        if self.timeframe_analysis is None:
            self.timeframe_analysis = {}
        if not self.generated_at:
            self.generated_at = datetime.now().isoformat()


class EventContractSignalGeneratorSimple:
    """简化版事件合约信号生成器"""
    
    def __init__(self, 
                 signal_threshold: float = 55.0,  # 🆕 降低阈值从90%到55%
                 min_timeframe_consensus: int = 1,  # 🆕 降低共识要求从2到1
                 confidence_threshold: float = 30.0):  # 🆕 降低置信度阈值从80%到30%
        """
        初始化信号生成器
        
        Args:
            signal_threshold: 信号生成阈值（概率百分比）- 降低以增加信号频率
            min_timeframe_consensus: 最少时间周期共识数量 - 降低以增加信号频率
            confidence_threshold: 置信度阈值 - 降低以允许更多低置信度信号
        """
        self.signal_threshold = signal_threshold
        self.min_timeframe_consensus = min_timeframe_consensus
        self.confidence_threshold = confidence_threshold
        
        # K线数据存储
        self.klines = {
            '1m': deque(maxlen=200),
            '5m': deque(maxlen=200),
            '15m': deque(maxlen=200),
            '30m': deque(maxlen=200),
            '1h': deque(maxlen=200)
        }
        
        # 1分钟K线聚合缓存
        self._minute_buffer = deque(maxlen=60)
        
        print(f"✅ 简化版信号生成器初始化完成: threshold={signal_threshold}%, "
              f"min_consensus={min_timeframe_consensus}, confidence_threshold={confidence_threshold}%")
        print("🎯 新配置：更频繁的信号生成，通过置信度评分区分信号质量")
    
    def add_kline_data(self, timestamp: int, open_price: float, high_price: float, 
                      low_price: float, close_price: float, volume: float):
        """
        添加1分钟K线数据
        
        Args:
            timestamp: 时间戳（毫秒）
            open_price: 开盘价
            high_price: 最高价
            low_price: 最低价
            close_price: 收盘价
            volume: 成交量
        """
        kline = KlineData(
            timestamp=timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=volume
        )
        
        # 添加到1分钟序列
        self.klines['1m'].append(kline)
        self._minute_buffer.append(kline)
        
        # 聚合生成更大时间周期的K线
        self._aggregate_timeframes(kline)
    
    def _aggregate_timeframes(self, current_kline: KlineData):
        """聚合生成更大时间周期的K线"""
        dt = datetime.fromtimestamp(current_kline.timestamp / 1000)
        
        # 5分钟K线聚合
        if dt.minute % 5 == 4:
            self._aggregate_period('5m', 5)
        
        # 15分钟K线聚合
        if dt.minute % 15 == 14:
            self._aggregate_period('15m', 15)
        
        # 30分钟K线聚合
        if dt.minute % 30 == 29:
            self._aggregate_period('30m', 30)
        
        # 1小时K线聚合
        if dt.minute == 59:
            self._aggregate_period('1h', 60)
    
    def _aggregate_period(self, timeframe: str, minutes: int):
        """聚合指定时间周期的K线"""
        if len(self._minute_buffer) < minutes:
            return
        
        recent_klines = list(self._minute_buffer)[-minutes:]
        if not recent_klines:
            return
        
        # 聚合数据
        open_price = recent_klines[0].open
        close_price = recent_klines[-1].close
        high_price = max(k.high for k in recent_klines)
        low_price = min(k.low for k in recent_klines)
        total_volume = sum(k.volume for k in recent_klines)
        
        # 创建聚合K线
        aggregated_kline = KlineData(
            timestamp=recent_klines[-1].timestamp,
            open=open_price,
            high=high_price,
            low=low_price,
            close=close_price,
            volume=total_volume
        )
        
        # 添加到对应时间周期
        self.klines[timeframe].append(aggregated_kline)
    
    def calculate_indicators(self, klines: List[KlineData]) -> TechnicalIndicators:
        """计算技术指标"""
        if len(klines) < 10:  # 🆕 进一步降低要求到10根K线
            return TechnicalIndicators()
        
        # 转换为numpy数组
        closes = np.array([k.close for k in klines])
        highs = np.array([k.high for k in klines])
        lows = np.array([k.low for k in klines])
        volumes = np.array([k.volume for k in klines])
        
        indicators = TechnicalIndicators()
        
        try:
            if TALIB_AVAILABLE:
                # 使用talib计算技术指标
                indicators.sma_5 = float(talib.SMA(closes, timeperiod=5)[-1])
                indicators.sma_10 = float(talib.SMA(closes, timeperiod=10)[-1])
                indicators.sma_20 = float(talib.SMA(closes, timeperiod=min(20, len(closes)))[-1])
                indicators.ema_5 = float(talib.EMA(closes, timeperiod=5)[-1])
                indicators.ema_10 = float(talib.EMA(closes, timeperiod=10)[-1])
                indicators.ema_20 = float(talib.EMA(closes, timeperiod=min(20, len(closes)))[-1])
                
                # RSI指标
                indicators.rsi_14 = float(talib.RSI(closes, timeperiod=min(14, len(closes)))[-1])
                indicators.rsi_21 = float(talib.RSI(closes, timeperiod=min(21, len(closes)))[-1])
                
                # 随机指标
                stoch_k, stoch_d = talib.STOCH(highs, lows, closes, 
                                              fastk_period=min(14, len(closes)), slowk_period=3, slowd_period=3)
                indicators.stoch_k = float(stoch_k[-1])
                indicators.stoch_d = float(stoch_d[-1])
                
                # MACD指标
                macd_line, macd_signal, macd_histogram = talib.MACD(closes, 
                                                                   fastperiod=min(12, len(closes)), 
                                                                   slowperiod=min(26, len(closes)), 
                                                                   signalperiod=min(9, len(closes)))
                indicators.macd_line = float(macd_line[-1])
                indicators.macd_signal = float(macd_signal[-1])
                indicators.macd_histogram = float(macd_histogram[-1])
                
                # 布林带
                bb_upper, bb_middle, bb_lower = talib.BBANDS(closes, timeperiod=min(20, len(closes)), nbdevup=2, nbdevdn=2)
                indicators.bb_upper = float(bb_upper[-1])
                indicators.bb_middle = float(bb_middle[-1])
                indicators.bb_lower = float(bb_lower[-1])
                
                # ATR
                indicators.atr = float(talib.ATR(highs, lows, closes, timeperiod=min(14, len(closes)))[-1])
                
            else:
                # 使用简化版技术指标计算
                closes_list = closes.tolist()
                highs_list = highs.tolist()
                lows_list = lows.tolist()
                volumes_list = volumes.tolist()
                
                # 移动平均线
                sma_5 = calculate_sma(closes_list, 5)
                sma_10 = calculate_sma(closes_list, 10)
                sma_20 = calculate_sma(closes_list, min(20, len(closes_list)))
                ema_5 = calculate_ema(closes_list, 5)
                ema_10 = calculate_ema(closes_list, 10)
                ema_20 = calculate_ema(closes_list, min(20, len(closes_list)))
                
                indicators.sma_5 = sma_5[-1] if not pd.isna(sma_5[-1]) else 0.0
                indicators.sma_10 = sma_10[-1] if not pd.isna(sma_10[-1]) else 0.0
                indicators.sma_20 = sma_20[-1] if not pd.isna(sma_20[-1]) else 0.0
                indicators.ema_5 = ema_5[-1] if not pd.isna(ema_5[-1]) else 0.0
                indicators.ema_10 = ema_10[-1] if not pd.isna(ema_10[-1]) else 0.0
                indicators.ema_20 = ema_20[-1] if not pd.isna(ema_20[-1]) else 0.0
                
                # RSI指标
                rsi_14 = calculate_rsi(closes_list, min(14, len(closes_list)))
                rsi_21 = calculate_rsi(closes_list, min(21, len(closes_list)))
                indicators.rsi_14 = rsi_14[-1] if not pd.isna(rsi_14[-1]) else 50.0
                indicators.rsi_21 = rsi_21[-1] if not pd.isna(rsi_21[-1]) else 50.0
                
                # 随机指标
                stoch_k, stoch_d = calculate_stochastic(highs_list, lows_list, closes_list, min(14, len(closes_list)), 3)
                indicators.stoch_k = stoch_k[-1] if not pd.isna(stoch_k[-1]) else 50.0
                indicators.stoch_d = stoch_d[-1] if not pd.isna(stoch_d[-1]) else 50.0
                
                # MACD指标
                macd_line, macd_signal, macd_histogram = calculate_macd(closes_list, 
                                                                       min(12, len(closes_list)), 
                                                                       min(26, len(closes_list)), 
                                                                       min(9, len(closes_list)))
                indicators.macd_line = macd_line[-1] if not pd.isna(macd_line[-1]) else 0.0
                indicators.macd_signal = macd_signal[-1] if not pd.isna(macd_signal[-1]) else 0.0
                indicators.macd_histogram = macd_histogram[-1] if not pd.isna(macd_histogram[-1]) else 0.0
                
                # 布林带
                bb_upper, bb_middle, bb_lower = calculate_bollinger_bands(closes_list, min(20, len(closes_list)), 2)
                indicators.bb_upper = bb_upper[-1] if not pd.isna(bb_upper[-1]) else closes[-1]
                indicators.bb_middle = bb_middle[-1] if not pd.isna(bb_middle[-1]) else closes[-1]
                indicators.bb_lower = bb_lower[-1] if not pd.isna(bb_lower[-1]) else closes[-1]
                
                # ATR
                atr = calculate_atr(highs_list, lows_list, closes_list, min(14, len(closes_list)))
                indicators.atr = atr[-1] if not pd.isna(atr[-1]) else 0.0
                
                # 计算波动率
                indicators.volatility = calculate_volatility(closes_list, min(14, len(closes_list)))
                
            # 布林带位置
            if indicators.bb_upper > indicators.bb_lower:
                indicators.bb_position = (closes[-1] - indicators.bb_lower) / (indicators.bb_upper - indicators.bb_lower)
            else:
                indicators.bb_position = 0.5
                
            # 成交量指标
            if len(volumes) >= 10:
                indicators.volume_sma = np.mean(volumes[-10:])
                indicators.volume_ratio = volumes[-1] / indicators.volume_sma if indicators.volume_sma > 0 else 1.0
            else:
                indicators.volume_sma = volumes[-1] if len(volumes) > 0 else 0.0
                indicators.volume_ratio = 1.0
                
        except Exception as e:
            print(f"⚠️ 计算技术指标时出错: {e}")
            # 返回默认值
            indicators.rsi_14 = 50.0
            indicators.rsi_21 = 50.0
            indicators.stoch_k = 50.0
            indicators.stoch_d = 50.0
            indicators.bb_position = 0.5
            indicators.volume_ratio = 1.0
            
        return indicators
    
    def _calculate_confidence_score(self, 
                                  latest_kline: KlineData, 
                                  klines_15m: List[KlineData], 
                                  indicators: TechnicalIndicators,
                                  bullish_prob: float,
                                  bearish_prob: float) -> Dict[str, Any]:
        """
        🆕 计算置信度评分系统
        
        Args:
            latest_kline: 最新K线
            klines_15m: 15分钟K线数据
            indicators: 技术指标
            bullish_prob: 看涨概率
            bearish_prob: 看跌概率
            
        Returns:
            置信度评分详情
        """
        confidence_factors = {}
        total_score = 0
        max_score = 0
        
        # 1. 价格波动性评分 (0-25分)
        price_volatility_score = 0
        if len(klines_15m) >= 3:
            recent_prices = [k.close for k in klines_15m[-3:]]
            price_range = (max(recent_prices) - min(recent_prices)) / min(recent_prices) * 100
            
            if price_range >= 2.0:  # 2%以上波动
                price_volatility_score = 25
            elif price_range >= 1.0:  # 1-2%波动
                price_volatility_score = 20
            elif price_range >= 0.5:  # 0.5-1%波动
                price_volatility_score = 15
            elif price_range >= 0.2:  # 0.2-0.5%波动
                price_volatility_score = 10
            else:  # <0.2%波动
                price_volatility_score = 5
                
            confidence_factors['price_volatility'] = {
                'score': price_volatility_score,
                'value': price_range,
                'description': f'价格波动性: {price_range:.2f}%'
            }
        else:
            confidence_factors['price_volatility'] = {
                'score': 10,
                'value': 0,
                'description': '价格波动性: 数据不足'
            }
            price_volatility_score = 10
            
        total_score += price_volatility_score
        max_score += 25
        
        # 2. 交易量评分 (0-25分)
        volume_score = 0
        if indicators.volume_ratio >= 2.0:  # 成交量是平均的2倍以上
            volume_score = 25
        elif indicators.volume_ratio >= 1.5:  # 1.5-2倍
            volume_score = 20
        elif indicators.volume_ratio >= 1.2:  # 1.2-1.5倍
            volume_score = 15
        elif indicators.volume_ratio >= 0.8:  # 0.8-1.2倍
            volume_score = 10
        else:  # <0.8倍
            volume_score = 5
            
        confidence_factors['volume'] = {
            'score': volume_score,
            'value': indicators.volume_ratio,
            'description': f'成交量比率: {indicators.volume_ratio:.2f}x'
        }
        
        total_score += volume_score
        max_score += 25
        
        # 3. 市场动能评分 (0-25分)
        momentum_score = 0
        
        # RSI动能
        rsi_momentum = 0
        if indicators.rsi_14 >= 70 or indicators.rsi_14 <= 30:  # 超买超卖
            rsi_momentum = 10
        elif indicators.rsi_14 >= 60 or indicators.rsi_14 <= 40:  # 偏强偏弱
            rsi_momentum = 7
        else:  # 中性
            rsi_momentum = 3
            
        # MACD动能
        macd_momentum = 0
        if abs(indicators.macd_histogram) >= 0.5:  # 强势MACD
            macd_momentum = 10
        elif abs(indicators.macd_histogram) >= 0.2:  # 中等MACD
            macd_momentum = 7
        else:  # 弱势MACD
            macd_momentum = 3
            
        # 布林带位置动能
        bb_momentum = 0
        if indicators.bb_position >= 0.8 or indicators.bb_position <= 0.2:  # 接近边界
            bb_momentum = 5
        elif indicators.bb_position >= 0.7 or indicators.bb_position <= 0.3:  # 偏向边界
            bb_momentum = 3
        else:  # 中间位置
            bb_momentum = 1
            
        momentum_score = rsi_momentum + macd_momentum + bb_momentum
        momentum_score = min(momentum_score, 25)  # 限制最大值
        
        confidence_factors['momentum'] = {
            'score': momentum_score,
            'rsi': indicators.rsi_14,
            'macd_hist': indicators.macd_histogram,
            'bb_position': indicators.bb_position,
            'description': f'市场动能: RSI={indicators.rsi_14:.1f}, MACD={indicators.macd_histogram:.3f}, BB位置={indicators.bb_position:.2f}'
        }
        
        total_score += momentum_score
        max_score += 25
        
        # 4. 信号强度评分 (0-25分)
        signal_strength_score = 0
        max_prob = max(bullish_prob, bearish_prob)
        prob_diff = abs(bullish_prob - bearish_prob)
        
        if max_prob >= 80:  # 80%以上概率
            signal_strength_score = 25
        elif max_prob >= 70:  # 70-80%概率
            signal_strength_score = 20
        elif max_prob >= 60:  # 60-70%概率
            signal_strength_score = 15
        elif max_prob >= 55:  # 55-60%概率
            signal_strength_score = 10
        else:  # <55%概率
            signal_strength_score = 5
            
        # 概率差异加成
        if prob_diff >= 20:  # 20%以上差异
            signal_strength_score += 5
        elif prob_diff >= 10:  # 10-20%差异
            signal_strength_score += 3
        elif prob_diff >= 5:  # 5-10%差异
            signal_strength_score += 1
            
        signal_strength_score = min(signal_strength_score, 25)
        
        confidence_factors['signal_strength'] = {
            'score': signal_strength_score,
            'max_prob': max_prob,
            'prob_diff': prob_diff,
            'description': f'信号强度: 最大概率={max_prob:.1f}%, 差异={prob_diff:.1f}%'
        }
        
        total_score += signal_strength_score
        max_score += 25
        
        # 计算最终置信度百分比
        confidence_percentage = (total_score / max_score) * 100
        
        # 确定置信度等级
        if confidence_percentage >= 80:
            confidence_level = "高"
            confidence_color = "🟢"
        elif confidence_percentage >= 60:
            confidence_level = "中"
            confidence_color = "🟡"
        elif confidence_percentage >= 40:
            confidence_level = "低"
            confidence_color = "🟠"
        else:
            confidence_level = "极低"
            confidence_color = "🔴"
            
        return {
            'total_score': total_score,
            'max_score': max_score,
            'confidence_percentage': confidence_percentage,
            'confidence_level': confidence_level,
            'confidence_color': confidence_color,
            'factors': confidence_factors,
            'criteria': {
                'price_volatility': '价格波动性 (0-25分): 基于最近3根K线的价格波动范围',
                'volume': '成交量 (0-25分): 当前成交量与平均成交量的比率',
                'momentum': '市场动能 (0-25分): RSI、MACD、布林带位置的综合评分',
                'signal_strength': '信号强度 (0-25分): 基于概率预测的强度和差异'
            }
        }
    
    def _force_generate_signal(self, 
                             klines_15m: List[KlineData], 
                             timeframe_analysis: Dict[str, Dict]) -> SignalResult:
        """
        🆕 强制生成信号 - 确保每根15分钟K线都有信号
        
        Args:
            klines_15m: 15分钟K线数据
            timeframe_analysis: 时间周期分析结果
            
        Returns:
            强制生成的信号结果
        """
        print("🎯 强制信号生成模式：确保产生可操作信号")
        
        # 获取15分钟时间周期的分析结果
        primary_analysis = timeframe_analysis.get('15m', {})
        if not primary_analysis:
            # 如果没有15分钟分析，使用其他可用的时间周期
            for tf in ['5m', '30m', '1h']:
                if tf in timeframe_analysis:
                    primary_analysis = timeframe_analysis[tf]
                    print(f"📊 使用 {tf} 时间周期作为主要分析")
                    break
                    
        if not primary_analysis:
            print("❌ 没有可用的时间周期分析")
            return SignalResult(has_signal=False, direction="NEUTRAL")
            
        # 获取概率和指标
        bullish_prob = primary_analysis.get('bullish_probability', 50.0)
        bearish_prob = primary_analysis.get('bearish_probability', 50.0)
        indicators = primary_analysis.get('indicators', TechnicalIndicators())
        latest_kline = primary_analysis.get('latest_kline')
        
        # 确定信号方向 - 即使概率差异很小也要做出决定
        if bullish_prob >= bearish_prob:
            direction = "UP"
            signal_probability = bullish_prob
        else:
            direction = "DOWN"
            signal_probability = bearish_prob
            
        # 计算置信度评分
        confidence_details = self._calculate_confidence_score(
            latest_kline, klines_15m, indicators, bullish_prob, bearish_prob
        )
        
        # 使用置信度评分作为最终置信度
        final_confidence = confidence_details['confidence_percentage']
        
        # 计算技术评分
        technical_score = min(100, (
            (indicators.rsi_14 - 50) ** 2 / 25 +  # RSI偏离中性的程度
            abs(indicators.macd_histogram) * 100 +  # MACD强度
            abs(indicators.bb_position - 0.5) * 200 +  # 布林带位置偏离
            indicators.volume_ratio * 20  # 成交量比率
        ))
        
        # 创建信号结果
        signal_result = SignalResult(
            has_signal=True,  # 🆕 强制设为True
            direction=direction,
            confidence=final_confidence,
            bullish_probability=bullish_prob,
            bearish_probability=bearish_prob,
            supporting_timeframes=[tf for tf in timeframe_analysis.keys()],
            technical_score=technical_score,
            risk_level=self._determine_risk_level(final_confidence),
            timeframe_analysis=timeframe_analysis,
            generated_at=datetime.now().isoformat()
        )
        
        # 添加置信度详情到timeframe_analysis
        signal_result.timeframe_analysis['confidence_details'] = confidence_details
        
        print(f"✅ 强制生成信号: {direction} - 置信度: {final_confidence:.1f}% ({confidence_details['confidence_level']})")
        print(f"📊 概率分布: 看涨={bullish_prob:.1f}%, 看跌={bearish_prob:.1f}%")
        print(f"🎯 置信度评分: {confidence_details['total_score']}/{confidence_details['max_score']} = {final_confidence:.1f}%")
        
        return signal_result
    
    def _determine_risk_level(self, confidence: float) -> str:
        """根据置信度确定风险等级"""
        if confidence >= 80:
            return "LOW"
        elif confidence >= 60:
            return "MEDIUM"
        elif confidence >= 40:
            return "HIGH"
        else:
            return "VERY_HIGH"
    
    def analyze_probability(self, klines: List[KlineData], indicators: TechnicalIndicators) -> Tuple[float, float]:
        """分析下一根K线方向的概率"""
        if len(klines) < 20:
            return 50.0, 50.0
        
        bullish_score = 0
        bearish_score = 0
        total_score = 0
        
        # RSI分析
        if indicators.rsi_14 < 30:  # 超卖
            bullish_score += 30
        elif indicators.rsi_14 > 70:  # 超买
            bearish_score += 30
        elif indicators.rsi_14 < 45:
            bullish_score += 10
        elif indicators.rsi_14 > 55:
            bearish_score += 10
        total_score += 30
        
        # MACD分析
        if indicators.macd_histogram > 0:
            if indicators.macd_line > indicators.macd_signal:
                bullish_score += 25
            else:
                bullish_score += 15
        else:
            if indicators.macd_line < indicators.macd_signal:
                bearish_score += 25
            else:
                bearish_score += 15
        total_score += 25
        
        # 布林带分析
        if indicators.bb_position < 0.2:  # 接近下轨
            bullish_score += 25
        elif indicators.bb_position > 0.8:  # 接近上轨
            bearish_score += 25
        elif indicators.bb_position < 0.4:
            bullish_score += 10
        elif indicators.bb_position > 0.6:
            bearish_score += 10
        total_score += 25
        
        # EMA趋势分析
        if indicators.ema_5 > indicators.ema_10 > indicators.ema_20:
            bullish_score += 20  # 强烈看涨
        elif indicators.ema_5 > indicators.ema_10:
            bullish_score += 10  # 轻微看涨
        elif indicators.ema_5 < indicators.ema_10 < indicators.ema_20:
            bearish_score += 20  # 强烈看跌
        elif indicators.ema_5 < indicators.ema_10:
            bearish_score += 10  # 轻微看跌
        total_score += 20
        
        # 归一化为概率
        if total_score > 0:
            bullish_prob = (bullish_score / total_score) * 100
            bearish_prob = (bearish_score / total_score) * 100
        else:
            bullish_prob = bearish_prob = 50.0
        
        # 确保概率和为100%
        total_prob = bullish_prob + bearish_prob
        if total_prob > 0:
            bullish_prob = (bullish_prob / total_prob) * 100
            bearish_prob = (bearish_prob / total_prob) * 100
        else:
            bullish_prob = bearish_prob = 50.0
        
        return bullish_prob, bearish_prob
    
    
    def _evaluate_market_quality(self, latest_kline: KlineData, klines_15m: List[KlineData]) -> Dict:
        """
        评估市场质量，判断是否适合交易
        
        Args:
            latest_kline: 最新的15分钟K线
            klines_15m: 15分钟K线列表
            
        Returns:
            包含市场质量评估结果的字典
        """
        quality_checks = {
            'suitable_for_trading': True,
            'reason': '',
            'amplitude_check': True,
            'volatility_check': True,
            'trend_significance': True,
            'volume_check': True,
            'quality_score': 100
        }
        
        # 1. 检查K线实体大小（振幅检查）
        amplitude = abs(latest_kline.close - latest_kline.open) / latest_kline.open * 100
        if amplitude < 0.5:  # 振幅不足0.5%
            quality_checks['amplitude_check'] = False
            quality_checks['quality_score'] -= 30
            quality_checks['reason'] += f"K线振幅过小({amplitude:.2f}%<0.5%); "
        
        # 2. 检查波动范围
        high_low_range = (latest_kline.high - latest_kline.low) / latest_kline.low * 100
        if high_low_range < 0.8:  # 高低点波动不足0.8%
            quality_checks['volatility_check'] = False
            quality_checks['quality_score'] -= 25
            quality_checks['reason'] += f"波动范围过小({high_low_range:.2f}%<0.8%); "
        
        # 3. 检查趋势显著性（最近3根K线）
        if len(klines_15m) >= 3:
            recent_3_klines = klines_15m[-3:]
            
            # 计算价格变化
            price_changes = []
            for i in range(1, len(recent_3_klines)):
                change = (recent_3_klines[i].close - recent_3_klines[i-1].close) / recent_3_klines[i-1].close * 100
                price_changes.append(change)
            
            # 检查是否有显著趋势
            total_change = sum(abs(change) for change in price_changes)
            if total_change < 1.0:  # 最近3根K线总变化不足1%
                quality_checks['trend_significance'] = False
                quality_checks['quality_score'] -= 25
                quality_checks['reason'] += f"趋势不显著(3K变化{total_change:.2f}%<1%); "
            
            # 检查方向一致性
            positive_changes = sum(1 for change in price_changes if change > 0.2)
            negative_changes = sum(1 for change in price_changes if change < -0.2)
            
            if positive_changes == 0 and negative_changes == 0:
                quality_checks['trend_significance'] = False
                quality_checks['quality_score'] -= 20
                quality_checks['reason'] += "缺乏明确方向; "
        
        # 4. 检查成交量（相对于前几根K线）
        if len(klines_15m) >= 5:
            recent_volumes = [k.volume for k in klines_15m[-5:]]
            avg_volume = sum(recent_volumes[:-1]) / (len(recent_volumes) - 1)
            
            current_volume_ratio = latest_kline.volume / avg_volume if avg_volume > 0 else 1.0
            
            # 成交量过于萎缩（低于平均70%）或过度放大（超过平均300%）都可能不适合
            if current_volume_ratio < 0.7:
                quality_checks['volume_check'] = False
                quality_checks['quality_score'] -= 15
                quality_checks['reason'] += f"成交量萎缩({current_volume_ratio:.1f}x<0.7x); "
            elif current_volume_ratio > 3.0:
                quality_checks['volume_check'] = False
                quality_checks['quality_score'] -= 10
                quality_checks['reason'] += f"成交量异常放大({current_volume_ratio:.1f}x>3.0x); "
        
        # 5. 检查市场是否处于整理状态（布林带收敛）
        if len(klines_15m) >= 20:
            recent_closes = [k.close for k in klines_15m[-20:]]
            
            # 简单的布林带计算
            sma_20 = sum(recent_closes) / len(recent_closes)
            variance = sum((price - sma_20) ** 2 for price in recent_closes) / len(recent_closes)
            std_dev = variance ** 0.5
            
            bb_width = (std_dev * 2) / sma_20 * 100  # 布林带宽度百分比
            
            if bb_width < 2.0:  # 布林带过于收敛
                quality_checks['quality_score'] -= 15
                quality_checks['reason'] += f"市场整理中(BB宽度{bb_width:.1f}%<2%); "
        
        # 6. 综合评分判断
        if quality_checks['quality_score'] < 50:
            quality_checks['suitable_for_trading'] = False
            if not quality_checks['reason']:
                quality_checks['reason'] = f"综合质量评分过低({quality_checks['quality_score']}/100)"
        
        # 清理原因字符串
        quality_checks['reason'] = quality_checks['reason'].rstrip('; ')
        
        if quality_checks['suitable_for_trading']:
            quality_checks['reason'] = f"市场质量良好(评分:{quality_checks['quality_score']}/100)"
        
        return quality_checks
    
    def _check_clear_entry_opportunity(self, klines_15m: List[KlineData], indicators: TechnicalIndicators) -> Dict:
        """
        检查是否有清晰的入场机会
        
        Args:
            klines_15m: 15分钟K线列表
            indicators: 技术指标
            
        Returns:
            入场机会评估结果
        """
        entry_opportunity = {
            'has_clear_opportunity': False,
            'opportunity_type': '',
            'strength': 0,
            'reason': ''
        }
        
        if not indicators or len(klines_15m) < 3:
            entry_opportunity['reason'] = "数据不足"
            return entry_opportunity
        
        strength_score = 0
        opportunity_signals = []
        
        # 1. 检查超买/超卖机会
        if indicators.rsi_14 < 25:  # 强超卖
            strength_score += 30
            opportunity_signals.append("RSI强超卖")
        elif indicators.rsi_14 > 75:  # 强超买
            strength_score += 30
            opportunity_signals.append("RSI强超买")
        elif indicators.rsi_14 < 35:  # 温和超卖
            strength_score += 15
            opportunity_signals.append("RSI温和超卖")
        elif indicators.rsi_14 > 65:  # 温和超买
            strength_score += 15
            opportunity_signals.append("RSI温和超买")
        
        # 2. 检查布林带极值
        if indicators.bb_position < 0.1:  # 价格接近布林带下轨
            strength_score += 25
            opportunity_signals.append("布林带下轨反弹")
        elif indicators.bb_position > 0.9:  # 价格接近布林带上轨
            strength_score += 25
            opportunity_signals.append("布林带上轨回调")
        
        # 3. 检查MACD背离或金叉死叉
        if indicators.macd_histogram > 0.05:  # MACD强势金叉
            strength_score += 20
            opportunity_signals.append("MACD强势金叉")
        elif indicators.macd_histogram < -0.05:  # MACD强势死叉
            strength_score += 20
            opportunity_signals.append("MACD强势死叉")
        
        # 4. 检查趋势突破
        latest_kline = klines_15m[-1]
        if len(klines_15m) >= 10:
            # 检查是否突破最近高点或低点
            recent_highs = [k.high for k in klines_15m[-10:-1]]
            recent_lows = [k.low for k in klines_15m[-10:-1]]
            
            max_high = max(recent_highs)
            min_low = min(recent_lows)
            
            if latest_kline.high > max_high * 1.005:  # 突破前期高点
                strength_score += 25
                opportunity_signals.append("突破前期高点")
            elif latest_kline.low < min_low * 0.995:  # 跌破前期低点
                strength_score += 25
                opportunity_signals.append("跌破前期低点")
        
        # 5. 检查成交量确认
        if len(klines_15m) >= 5:
            recent_volumes = [k.volume for k in klines_15m[-5:]]
            avg_volume = sum(recent_volumes[:-1]) / (len(recent_volumes) - 1)
            volume_ratio = latest_kline.volume / avg_volume if avg_volume > 0 else 1.0
            
            if volume_ratio > 1.5:  # 成交量放大确认
                strength_score += 15
                opportunity_signals.append(f"成交量放大确认({volume_ratio:.1f}x)")
        
        # 评估入场机会
        entry_opportunity['strength'] = min(100, strength_score)
        entry_opportunity['opportunity_signals'] = opportunity_signals
        
        if strength_score >= 60:
            entry_opportunity['has_clear_opportunity'] = True
            entry_opportunity['opportunity_type'] = "强机会"
            entry_opportunity['reason'] = f"多重信号确认: {', '.join(opportunity_signals)}"
        elif strength_score >= 40:
            entry_opportunity['has_clear_opportunity'] = True
            entry_opportunity['opportunity_type'] = "中等机会"
            entry_opportunity['reason'] = f"部分信号确认: {', '.join(opportunity_signals)}"
        else:
            entry_opportunity['has_clear_opportunity'] = False
            entry_opportunity['opportunity_type'] = "机会不足"
            entry_opportunity['reason'] = f"信号强度不足({strength_score}/100): {', '.join(opportunity_signals) if opportunity_signals else '无明显信号'}"
        
        return entry_opportunity

    def _generate_user_reminder(self, signal_result: SignalResult, klines_15m: List[KlineData]) -> Tuple[str, str]:
        """
        生成用户提醒信息
        
        Args:
            signal_result: 信号结果
            klines_15m: 15分钟K线数据
            
        Returns:
            (user_reminder, market_status) 元组
        """
        
        if signal_result.has_signal:
            # 有信号时的提醒
            direction_text = "看涨" if signal_result.direction == "UP" else "看跌"
            direction_emoji = "🚀" if signal_result.direction == "UP" else "📉"
            
            reminder = f"""
📢 【交易信号提醒】
{direction_emoji} 检测到{direction_text}信号！建议关注交易机会
💡 信号强度: {signal_result.confidence:.1f}%
📊 技术评分: {signal_result.technical_score:.1f}/100
⚠️ 风险等级: {signal_result.risk_level}
🎯 请谨慎评估后决定是否交易
            """.strip()
            
            return reminder, "信号确认"
        
        else:
            # 无信号时的提醒
            if len(klines_15m) < 10:
                # 数据不足
                reminder = f"""
📢 【系统状态提醒】
⏳ 系统正在收集数据中...
📊 当前15分钟K线: {len(klines_15m)}/10根
💡 需要更多历史数据来进行准确分析
🔄 请耐心等待数据积累
                """.strip()
                
                return reminder, "数据收集中"
            
            else:
                # 分析无信号的具体原因
                market_quality = signal_result.timeframe_analysis.get('market_quality', {})
                entry_opportunity = signal_result.timeframe_analysis.get('entry_opportunity', {})
                
                if market_quality and not market_quality.get('suitable_for_trading', True):
                    # 市场质量不佳
                    quality_score = market_quality.get('quality_score', 0)
                    quality_reason = market_quality.get('reason', '市场条件不佳')
                    
                    reminder = f"""
📢 【市场状态提醒】
🔍 系统正在监控中，暂无交易信号
📊 市场质量评分: {quality_score}/100
📝 当前状况: {quality_reason}
💡 建议: 等待更好的市场条件
⏰ 系统将持续监控，有信号时会及时提醒
                    """.strip()
                    
                    return reminder, "市场质量过滤"
                
                elif entry_opportunity and not entry_opportunity.get('has_clear_opportunity', True):
                    # 入场机会不明确
                    opportunity_reason = entry_opportunity.get('reason', '入场机会不明确')
                    opportunity_strength = entry_opportunity.get('strength', 0)
                    
                    reminder = f"""
📢 【交易机会提醒】
🎯 系统正在分析中，暂无明确入场机会
📊 机会强度: {opportunity_strength}/100
📝 当前状况: {opportunity_reason}
💡 建议: 等待更清晰的技术信号
⏰ 系统将持续监控，发现机会时会及时提醒
                    """.strip()
                    
                    return reminder, "等待入场机会"
                
                else:
                    # 一般情况：概率不足
                    bullish_prob = signal_result.bullish_probability
                    bearish_prob = signal_result.bearish_probability
                    threshold = self.signal_threshold
                    
                    # 判断距离阈值的接近程度
                    max_prob = max(bullish_prob, bearish_prob)
                    distance_to_threshold = threshold - max_prob
                    
                    if distance_to_threshold <= 10:
                        # 接近阈值
                        reminder = f"""
📢 【信号监控提醒】
🔍 系统检测到市场活跃度上升
📊 当前概率: 看涨{bullish_prob:.1f}% | 看跌{bearish_prob:.1f}%
📈 距离信号阈值: {distance_to_threshold:.1f}%
💡 建议: 保持关注，信号可能即将出现
⏰ 系统将持续监控价格变化
                        """.strip()
                        
                        return reminder, "信号待确认"
                    
                    else:
                        # 距离阈值较远
                        reminder = f"""
📢 【常规监控提醒】
📊 系统正在持续监控市场状态
📈 当前概率: 看涨{bullish_prob:.1f}% | 看跌{bearish_prob:.1f}%
🎯 信号阈值: {threshold:.1f}%
💡 建议: 市场方向尚不明确，耐心等待
⏰ 有明确信号时会立即提醒
                        """.strip()
                        
                        return reminder, "常规监控中"
    
    def _print_user_reminder(self, user_reminder: str, market_status: str):
        """
        打印用户提醒信息
        
        Args:
            user_reminder: 用户提醒文本
            market_status: 市场状态
        """
        print(f"\n{'='*50}")
        print(f"📱 用户提醒 - 市场状态: {market_status}")
        print(f"{'='*50}")
        print(user_reminder)
        print(f"{'='*50}")
        
        # 添加时间戳
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"⏰ 提醒时间: {timestamp}")
        print()

    def generate_signal(self) -> SignalResult:
        """生成交易信号"""
        print("\n🔄 开始生成交易信号...")
        
        # 检查是否有足够的数据
        klines_15m = list(self.klines['15m'])
        if len(klines_15m) < 10:
            print(f"❌ 15分钟K线数据不足: {len(klines_15m)}/10")
            # 创建数据不足的信号结果
            signal_result = SignalResult(
                has_signal=False,
                direction="NEUTRAL",
                confidence=0.0,
                technical_score=0.0
            )
            
            # 🆕 生成用户提醒信息
            user_reminder, market_status = self._generate_user_reminder(signal_result, klines_15m)
            signal_result.user_reminder = user_reminder
            signal_result.market_status = market_status
            
            # 🆕 打印用户提醒信息
            self._print_user_reminder(signal_result.user_reminder, signal_result.market_status)
            
            return signal_result
        
        print(f"✅ 15分钟K线数据充足: {len(klines_15m)}根")
        
        # 🆕 移除市场质量过滤 - 现在强制生成信号
        
        # 分析各时间周期
        timeframe_analysis = {}
        timeframes = ['5m', '15m', '30m', '1h']
        
        for timeframe in timeframes:
            klines = list(self.klines[timeframe])
            
            if len(klines) < 5:  # 🆕 进一步降低要求到5根K线
                print(f"⚠️ {timeframe}数据不足: {len(klines)}/5")
                continue
            
            # 计算技术指标
            indicators = self.calculate_indicators(klines)
            
            # 分析概率
            bullish_prob, bearish_prob = self.analyze_probability(klines, indicators)
            
            # 计算信号强度和置信度
            signal_strength = max(bullish_prob, bearish_prob)
            dominant_direction = "UP" if bullish_prob > bearish_prob else "DOWN"
            confidence = abs(bullish_prob - bearish_prob)
            
            timeframe_analysis[timeframe] = {
                'bullish_probability': bullish_prob,
                'bearish_probability': bearish_prob,
                'dominant_direction': dominant_direction,
                'signal_strength': signal_strength,
                'confidence': confidence,
                'indicators': indicators,
                'latest_kline': klines[-1] if klines else None
            }
            
            print(f"📊 {timeframe}: {dominant_direction} {signal_strength:.1f}% (置信度:{confidence:.1f}%)")
        
        if not timeframe_analysis:
            print("❌ 没有有效的时间周期分析结果")
            return SignalResult(has_signal=False)
        
        # 🆕 使用强制信号生成逻辑 - 确保每根15分钟K线都产生信号
        signal_result = self._force_generate_signal(klines_15m, timeframe_analysis)
        
        # 🆕 生成用户提醒信息
        user_reminder, market_status = self._generate_user_reminder(signal_result, klines_15m)
        signal_result.user_reminder = user_reminder
        signal_result.market_status = market_status
        
        # 打印详细的置信度分析
        if 'confidence_details' in signal_result.timeframe_analysis:
            confidence_details = signal_result.timeframe_analysis['confidence_details']
            print(f"\n📋 置信度分析详情:")
            print(f"   {confidence_details['confidence_color']} 置信度等级: {confidence_details['confidence_level']}")
            print(f"   📊 总评分: {confidence_details['total_score']}/{confidence_details['max_score']} = {confidence_details['confidence_percentage']:.1f}%")
            
            for factor_name, factor_data in confidence_details['factors'].items():
                print(f"   • {factor_data['description']} - {factor_data['score']}分")
        
        print(f"\n🎯 最终交易信号:")
        print(f"   方向: {signal_result.direction}")
        print(f"   置信度: {signal_result.confidence:.1f}%")
        print(f"   风险等级: {signal_result.risk_level}")
        print(f"   看涨概率: {signal_result.bullish_probability:.1f}%")
        print(f"   看跌概率: {signal_result.bearish_probability:.1f}%")
        print(f"   技术评分: {signal_result.technical_score:.1f}")
        
        # 🆕 打印用户提醒信息
        self._print_user_reminder(signal_result.user_reminder, signal_result.market_status)
        
        return signal_result
    
    def get_status(self) -> Dict:
        """获取信号生成器状态"""
        status = {}
        
        for timeframe in ['1m', '5m', '15m', '30m', '1h']:
            klines = list(self.klines[timeframe])
            latest_kline = klines[-1] if klines else None
            
            status[timeframe] = {
                'total_klines': len(klines),
                'latest_kline': {
                    'timestamp': latest_kline.timestamp if latest_kline else None,
                    'open': latest_kline.open if latest_kline else None,
                    'high': latest_kline.high if latest_kline else None,
                    'low': latest_kline.low if latest_kline else None,
                    'close': latest_kline.close if latest_kline else None,
                    'volume': latest_kline.volume if latest_kline else None,
                    'is_bullish': latest_kline.is_bullish if latest_kline else None,
                    'is_bearish': latest_kline.is_bearish if latest_kline else None
                } if latest_kline else None
            }
        
        return status